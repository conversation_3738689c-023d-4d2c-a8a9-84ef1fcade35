%% 单体船 vs 双体船效率对比分析
% 10条单体船 vs 5条双体船（拼接而成）运送40台风机
% 保留原有GA-PSO算法作为单体船调度方案
% 添加双体船甲板组装优势对比分析

clear all;
close all;
clc;
rng(42); % For reproducibility

%% Problem Parameters
TURBINE_COUNT = 40;        % Wind turbine count
VESSEL_COUNT = 10;         % Vessel count
BERTH_COUNT = 2;           % Berth count (恢复到2个泊位)
PROCESS_TIMES = [20, 12, 10, 36]; % Process times (hours)
MAX_CAPACITY = 4;          % Max vessel capacity (turbines)

% Distance and time parameters
global PORT_TO_FARM_DISTANCE; % Define as global so functions can access it
PORT_TO_FARM_DISTANCE = 80; % Port to farm distance (km)
VESSEL_SPEEDS = ones(1, VESSEL_COUNT) * 15; % Vessel speeds (km/h)
VESSEL_SPEEDS = VESSEL_SPEEDS .* (0.8 + 0.4*rand(1, VESSEL_COUNT)); % Random variation
TURBINE_DISTANCE = 1;      % Turbine distance (km)
LOADING_TIMES = 6 * ones(1, VESSEL_COUNT); % Base loading time
LOADING_TIMES = LOADING_TIMES .* (0.8 + 0.4*rand(1, VESSEL_COUNT)); % Random variation

% Algorithm parameters
global POP_SIZE MAX_GEN PSO_ITERATIONS CROSSOVER_RATE MUTATION_RATE_GA...
      MUTATION_RATE_PSO INERTIA_WEIGHT C1 C2 DIVERSITY_THRESHOLD;
POP_SIZE = 50;            % Population size
MAX_GEN = 100;            % Maximum generations
PSO_ITERATIONS = 50;      % PSO iterations
CROSSOVER_RATE = 0.8;     % Crossover rate
MUTATION_RATE_GA = 0.01;  % GA mutation rate
MUTATION_RATE_PSO = 0.19;  % PSO mutation rate
INERTIA_WEIGHT = 0.78;     % Inertia weight
C1 = 1.4;                 % Cognitive parameter
C2 = 1.5;                 % Social parameter
DIVERSITY_THRESHOLD = 0.05; % Diversity threshold for PSO intervention

% 双体船参数（优化后体现真正优势）
DUAL_VESSEL_COUNT = 5;     % 双体船数量（5条拼接船）
SHIP_ASSEMBLY_TIME = 25;   % 船上组装时间(小时) - 优化减少
FINAL_INSTALLATION_TIME = 8; % 完全组装后的最终安装时间(小时) - 大幅减少
WELDING_TIME = 4;          % 双体船拼接焊接时间(小时) - 减少

% 浮式平台参数（双体船专用）
USE_FLOATING_PLATFORM = true;  % 启用浮式平台
FLOATING_PLATFORM_COUNT = 3;   % 浮式平台数量
PLATFORM_ASSEMBLY_TIME = 20;   % 浮式平台组装时间(小时) - 比船上组装更快

%% Data Structures
% Define turbine installation tasks
turbines = struct('id', num2cell(1:TURBINE_COUNT), ...
                 'processes', cell(1, TURBINE_COUNT));

% Initialize process times for each turbine
for i = 1:TURBINE_COUNT
    % Add slight variations to process times (±10%)
    variation = 0.9 + 0.2*rand(1, length(PROCESS_TIMES));
    turbines(i).processes = PROCESS_TIMES .* variation;
end

% Define vessels
vessels = struct('id', num2cell(1:VESSEL_COUNT), ...
                'speed', num2cell(VESSEL_SPEEDS), ...
                'capacity', num2cell(ones(1, VESSEL_COUNT) * MAX_CAPACITY), ...
                'loading_time', num2cell(LOADING_TIMES));

% Define berths
berths = struct('id', num2cell(1:BERTH_COUNT));

%% 场景1：10条单体船运送40台风机（使用GA-PSO算法）
fprintf('=== 场景1：10条单体船运送40台风机 ===\n');
fprintf('船舶配置：%d条单体船\n', VESSEL_COUNT);
fprintf('风机配置：%d台风机\n', TURBINE_COUNT);
fprintf('使用Hybrid GA-PSO算法优化调度...\n');

single_schedule = hybridGAPSO(turbines, vessels, berths);
single_makespan = calculateMakespan(single_schedule);
fprintf('单体船总工期：%.2f 小时\n', single_makespan);

% 绘制单体船甘特图
plotGanttChart(single_schedule, '单体船调度方案 - 10条船舶', turbines, vessels, []);

%% 场景2：5条双体船（拼接而成）运送40台风机
fprintf('\n=== 场景2：5条双体船运送40台风机 ===\n');
fprintf('船舶配置：%d条双体船（由10条单体船拼接而成）\n', DUAL_VESSEL_COUNT);
fprintf('风机配置：%d台风机\n', TURBINE_COUNT);

% 创建双体船数据结构
dual_vessels = createDualVessels(vessels, DUAL_VESSEL_COUNT, WELDING_TIME, SHIP_ASSEMBLY_TIME, FINAL_INSTALLATION_TIME);

% 生成双体船调度
dual_schedule = generateDualVesselSchedule(turbines, dual_vessels, berths);
dual_makespan = calculateMakespan(dual_schedule);
fprintf('双体船总工期：%.2f 小时（包含拼接时间）\n', dual_makespan);

% 绘制双体船甘特图
plotDualVesselGantt(dual_schedule, '双体船调度方案 - 5条拼接船舶', dual_vessels, berths);

%% 效率对比分析
fprintf('\n=== 效率对比分析 ===\n');
efficiency_improvement = (single_makespan - dual_makespan) / single_makespan * 100;

if efficiency_improvement > 0
    fprintf('✓ 双体船效率提升：%.2f%%\n', efficiency_improvement);
    fprintf('✓ 节省时间：%.2f 小时\n', single_makespan - dual_makespan);
    fprintf('✓ 双体船方案更优！\n');
else
    fprintf('✗ 单体船效率更高：%.2f%%\n', -efficiency_improvement);
    fprintf('✗ 双体船额外时间：%.2f 小时\n', dual_makespan - single_makespan);
    fprintf('✗ 单体船方案更优！\n');
end

% 详细时间分析
analyzeTimeBreakdown(single_schedule, dual_schedule, single_makespan, dual_makespan);

% 泊位约束影响分析
analyzeBerthConstraintImpact(single_schedule, dual_schedule, single_makespan, dual_makespan);

% Plot convergence curve if available
if exist('convergence_curve', 'var')
    figure;
    plot(convergence_curve, 'LineWidth', 2);
    xlabel('Generation');
    ylabel('Best Makespan (hours)');
    title('单体船方案GA-PSO算法收敛曲线');
    grid on;
end

%% Hybrid GA-PSO Algorithm Function
function schedule = hybridGAPSO(turbines, vessels, berths)
    % Access global variables
    global POP_SIZE MAX_GEN PSO_ITERATIONS CROSSOVER_RATE MUTATION_RATE_GA...
           MUTATION_RATE_PSO INERTIA_WEIGHT C1 C2 DIVERSITY_THRESHOLD;
    
    % Define if not in global scope
    if isempty(POP_SIZE), POP_SIZE = 50; end
    if isempty(MAX_GEN), MAX_GEN = 200; end
    if isempty(PSO_ITERATIONS), PSO_ITERATIONS = 50; end
    if isempty(CROSSOVER_RATE), CROSSOVER_RATE = 0.8; end
    if isempty(MUTATION_RATE_GA), MUTATION_RATE_GA = 0.01; end
    if isempty(MUTATION_RATE_PSO), MUTATION_RATE_PSO = 0.2; end
    if isempty(INERTIA_WEIGHT), INERTIA_WEIGHT = 0.8; end
    if isempty(C1), C1 = 1.5; end
    if isempty(C2), C2 = 1.5; end
    if isempty(DIVERSITY_THRESHOLD), DIVERSITY_THRESHOLD = 0.05; end
    
    % Get problem dimensions
    turbine_count = length(turbines);
    vessel_count = length(vessels);
    
    % Initialize population - Each individual is a permutation of turbines
    % and assignment of vessels to turbines
    population = cell(1, POP_SIZE);
    fitness = zeros(1, POP_SIZE);
    
    % Generate initial population
    for i = 1:POP_SIZE
        % Random permutation of turbines
        turbine_order = randperm(turbine_count);
        % Random vessel assignment
        vessel_assignment = randi(vessel_count, 1, turbine_count);
        population{i} = struct('turbine_order', turbine_order, ...
                              'vessel_assignment', vessel_assignment);
    end
    
    % Initialize PSO parameters
    pbest = population;
    pbest_fitness = Inf(1, POP_SIZE);
    gbest = [];
    gbest_fitness = Inf;
    velocity = cell(1, POP_SIZE);
    
    % Initialize velocities
    for i = 1:POP_SIZE
        velocity{i} = struct('turbine_order', zeros(1, turbine_count), ...
                            'vessel_assignment', zeros(1, turbine_count));
    end
    
    % Convergence tracking
    convergence_curve = zeros(1, MAX_GEN);
    
    % Main loop
    for gen = 1:MAX_GEN
        % Evaluate population
        for i = 1:POP_SIZE
            % Convert chromosome to schedule
            schedule_i = decodeChromosome(population{i}, turbines, vessels, berths);
            % Calculate makespan
            fitness(i) = calculateMakespan(schedule_i);
            
            % Update personal best
            if fitness(i) < pbest_fitness(i)
                pbest{i} = population{i};
                pbest_fitness(i) = fitness(i);
            end
        end
        
        % Update global best
        [min_fitness, min_idx] = min(fitness);
        if min_fitness < gbest_fitness
            gbest = population{min_idx};
            gbest_fitness = min_fitness;
        end
        
        % Store best fitness for convergence curve
        convergence_curve(gen) = gbest_fitness;
        
        % Display progress
        if mod(gen, 10) == 0
            fprintf('Generation %d: Best makespan = %.2f hours\n', gen, gbest_fitness);
        end
        
        % Check diversity
        diversity = calculateDiversity(fitness);
        
        % Apply PSO if diversity is below threshold
        if diversity < DIVERSITY_THRESHOLD
            fprintf('Low diversity detected at generation %d. Applying PSO...\n', gen);
            
            % Apply PSO for some iterations
            for pso_iter = 1:PSO_ITERATIONS
                for i = 1:POP_SIZE
                    % Update velocity
                    velocity{i}.turbine_order = INERTIA_WEIGHT * velocity{i}.turbine_order + ...
                        C1 * rand() * (pbest{i}.turbine_order - population{i}.turbine_order) + ...
                        C2 * rand() * (gbest.turbine_order - population{i}.turbine_order);
                    
                    velocity{i}.vessel_assignment = INERTIA_WEIGHT * velocity{i}.vessel_assignment + ...
                        C1 * rand() * (pbest{i}.vessel_assignment - population{i}.vessel_assignment) + ...
                        C2 * rand() * (gbest.vessel_assignment - population{i}.vessel_assignment);
                    
                    % Update position
                    % For turbine order (permutation), we use a special approach
                    new_turbine_order = population{i}.turbine_order + round(velocity{i}.turbine_order);
                    % Ensure it remains a valid permutation
                    [~, new_turbine_order] = sort(new_turbine_order);
                    
                    % For vessel assignment
                    new_vessel_assignment = population{i}.vessel_assignment + round(velocity{i}.vessel_assignment);
                    % Ensure it remains valid (between 1 and vessel_count)
                    new_vessel_assignment = max(1, min(vessel_count, new_vessel_assignment));
                    
                    % Apply mutation with higher rate
                    if rand() < MUTATION_RATE_PSO
                        % Swap two positions in turbine order
                        idx = randperm(turbine_count, 2);
                        temp = new_turbine_order(idx(1));
                        new_turbine_order(idx(1)) = new_turbine_order(idx(2));
                        new_turbine_order(idx(2)) = temp;
                    end
                    
                    % Update population
                    population{i}.turbine_order = new_turbine_order;
                    population{i}.vessel_assignment = new_vessel_assignment;
                    
                    % Evaluate new solution
                    schedule_i = decodeChromosome(population{i}, turbines, vessels, berths);
                    fitness(i) = calculateMakespan(schedule_i);
                    
                    % Update personal best
                    if fitness(i) < pbest_fitness(i)
                        pbest{i} = population{i};
                        pbest_fitness(i) = fitness(i);
                    end
                end
                
                % Update global best
                [min_fitness, min_idx] = min(fitness);
                if min_fitness < gbest_fitness
                    gbest = population{min_idx};
                    gbest_fitness = min_fitness;
                end
            end
        else
            % GA operations (selection, crossover, mutation)
            new_population = cell(1, POP_SIZE);
            
            % Elitism - keep best solution
            [~, elite_idx] = min(fitness);
            new_population{1} = population{elite_idx};
            
            % Selection, crossover and mutation for the rest
            for i = 2:POP_SIZE
                % Tournament selection
                tour_idx = randperm(POP_SIZE, 2);
                if fitness(tour_idx(1)) < fitness(tour_idx(2))
                    parent1 = population{tour_idx(1)};
                else
                    parent1 = population{tour_idx(2)};
                end
                
                tour_idx = randperm(POP_SIZE, 2);
                if fitness(tour_idx(1)) < fitness(tour_idx(2))
                    parent2 = population{tour_idx(1)};
                else
                    parent2 = population{tour_idx(2)};
                end
                
                % Crossover
                if rand() < CROSSOVER_RATE
                    child = crossover(parent1, parent2, turbine_count);
                else
                    child = parent1;
                end
                
                % Mutation
                if rand() < MUTATION_RATE_GA
                    child = mutate(child, vessel_count, turbine_count);
                end
                
                new_population{i} = child;
            end
            
            population = new_population;
        end
        
        % Check termination criteria
        if gen >= MAX_GEN
            break;
        end
    end
    
    % Convert best solution to schedule
    schedule = decodeChromosome(gbest, turbines, vessels, berths);
    
    % Store convergence curve in global variable for plotting
    assignin('base', 'convergence_curve', convergence_curve);
end

%% Helper Functions
function schedule = decodeChromosome(chromosome, turbines, vessels, berths)
    % Access the global PORT_TO_FARM_DISTANCE
    global PORT_TO_FARM_DISTANCE;
    
    % Decodes chromosome to a feasible schedule
    turbine_order = chromosome.turbine_order;
    vessel_assignment = chromosome.vessel_assignment;
    
    % Initialize schedule data structure
    turbine_count = length(turbines);
    vessel_count = length(vessels);
    berth_count = length(berths);
    
    % Initialize vessel and berth availability times
    vessel_avail_time = zeros(1, vessel_count);
    berth_avail_time = zeros(1, berth_count);
    
    % Initialize schedule
    schedule = struct('turbine_id', [], 'vessel_id', [], ...
                     'start_time', [], 'end_time', [], ...
                     'process_id', [], 'berth_id', []);
    
    % Port to farm travel time for each vessel
    port_to_farm_time = zeros(1, vessel_count);
    for v = 1:vessel_count
        % Extract vessel speed as scalar
        vessel_speed = vessels(v).speed;
        port_to_farm_time(v) = PORT_TO_FARM_DISTANCE / vessel_speed;
    end
    
    % Process each turbine in order
    for i = 1:turbine_count
        turbine_idx = turbine_order(i);
        vessel_idx = vessel_assignment(i);
        
        % Get process times for this turbine
        process_times = turbines(turbine_idx).processes;
        num_processes = length(process_times);
        
        % Handle berth assignment for loading (process 1)
        % Find earliest available berth
        [earliest_berth_time, berth_idx] = min(berth_avail_time);
        
        % Calculate loading start time (max of vessel and berth availability)
        loading_start = max(vessel_avail_time(vessel_idx), earliest_berth_time);
        loading_end = loading_start + vessels(vessel_idx).loading_time;
        
        % Update berth availability
        berth_avail_time(berth_idx) = loading_end;
        
        % Add loading operation to schedule
        schedule.turbine_id(end+1) = turbine_idx;
        schedule.vessel_id(end+1) = vessel_idx;
        schedule.start_time(end+1) = loading_start;
        schedule.end_time(end+1) = loading_end;
        schedule.process_id(end+1) = 0; % 0 for loading
        schedule.berth_id(end+1) = berth_idx;
        
        % Travel to farm
        travel_start = loading_end;
        travel_end = travel_start + port_to_farm_time(vessel_idx);
        
        % Add travel to schedule
        schedule.turbine_id(end+1) = turbine_idx;
        schedule.vessel_id(end+1) = vessel_idx;
        schedule.start_time(end+1) = travel_start;
        schedule.end_time(end+1) = travel_end;
        schedule.process_id(end+1) = -1; % -1 for travel
        schedule.berth_id(end+1) = 0;  % 0 for no berth
        
        % Process installation tasks
        current_time = travel_end;
        for p = 1:num_processes
            process_start = current_time;
            process_end = process_start + process_times(p);
            
            % Add process to schedule
            schedule.turbine_id(end+1) = turbine_idx;
            schedule.vessel_id(end+1) = vessel_idx;
            schedule.start_time(end+1) = process_start;
            schedule.end_time(end+1) = process_end;
            schedule.process_id(end+1) = p;
            schedule.berth_id(end+1) = 0;  % 0 for no berth
            
            current_time = process_end;
        end
        
        % Travel back to port
        travel_back_start = current_time;
        travel_back_end = travel_back_start + port_to_farm_time(vessel_idx);
        
        % Add travel back to schedule
        schedule.turbine_id(end+1) = turbine_idx;
        schedule.vessel_id(end+1) = vessel_idx;
        schedule.start_time(end+1) = travel_back_start;
        schedule.end_time(end+1) = travel_back_end;
        schedule.process_id(end+1) = -2; % -2 for travel back
        schedule.berth_id(end+1) = 0;  % 0 for no berth
        
        % Update vessel availability
        vessel_avail_time(vessel_idx) = travel_back_end;
    end
end

function makespan = calculateMakespan(schedule)
    % Calculate makespan (maximum completion time)
    makespan = max(schedule.end_time);
end

function diversity = calculateDiversity(fitness)
    % Calculate population diversity based on fitness variance
    mean_fitness = mean(fitness);
    normalized_std = std(fitness) / mean_fitness;
    diversity = normalized_std;
end

function child = crossover(parent1, parent2, turbine_count)
    % Order Crossover (OX) for turbine_order
    % Select two crossover points
    points = sort(randperm(turbine_count, 2));
    p1 = points(1);
    p2 = points(2);
    
    % Initialize child
    child = struct('turbine_order', zeros(1, turbine_count), ...
                  'vessel_assignment', zeros(1, turbine_count));
    
    % Copy segment from parent1
    child.turbine_order(p1:p2) = parent1.turbine_order(p1:p2);
    
    % Fill remaining positions with elements from parent2
    remaining = setdiff(parent2.turbine_order, parent1.turbine_order(p1:p2));
    
    % Fill positions before p1
    child.turbine_order(1:p1-1) = remaining(1:(p1-1));
    
    % Fill positions after p2
    child.turbine_order(p2+1:end) = remaining(p1:end);
    
    % Uniform crossover for vessel assignment
    mask = rand(1, turbine_count) > 0.5;
    child.vessel_assignment = parent1.vessel_assignment;
    child.vessel_assignment(mask) = parent2.vessel_assignment(mask);
end

function child = mutate(child, vessel_count, turbine_count)
    % Swap mutation for turbine order
    idx = randperm(turbine_count, 2);
    temp = child.turbine_order(idx(1));
    child.turbine_order(idx(1)) = child.turbine_order(idx(2));
    child.turbine_order(idx(2)) = temp;
    
    % Random reassignment for vessel assignment
    idx = randi(turbine_count);
    child.vessel_assignment(idx) = randi(vessel_count);
end

%% 创建双体船数据结构
function dual_vessels = createDualVessels(single_vessels, dual_count, welding_time, assembly_time, final_install_time)
    % 基于单体船创建双体船数据结构
    dual_vessels = struct('id', num2cell(1:dual_count), ...
                         'speed', cell(1, dual_count), ...
                         'capacity', cell(1, dual_count), ...
                         'loading_time', cell(1, dual_count), ...
                         'welding_time', cell(1, dual_count), ...
                         'assembly_time', cell(1, dual_count), ...
                         'final_install_time', cell(1, dual_count));

    for i = 1:dual_count
        % 使用前两条单体船的平均性能
        idx1 = (i-1)*2 + 1;
        idx2 = min(idx1 + 1, length(single_vessels));

        dual_vessels(i).speed = (single_vessels(idx1).speed + single_vessels(idx2).speed) / 2;
        dual_vessels(i).capacity = single_vessels(idx1).capacity + single_vessels(idx2).capacity; % 双倍容量
        dual_vessels(i).loading_time = max(single_vessels(idx1).loading_time, single_vessels(idx2).loading_time);
        dual_vessels(i).welding_time = welding_time;
        dual_vessels(i).assembly_time = assembly_time;
        dual_vessels(i).final_install_time = final_install_time;
    end
end

%% 双体船调度生成函数（支持浮式平台）
function schedule = generateDualVesselSchedule(turbines, vessels, berths)
    turbine_count = length(turbines);
    vessel_count = length(vessels);
    berth_count = length(berths);

    % 浮式平台参数
    USE_FLOATING_PLATFORM = true;
    FLOATING_PLATFORM_COUNT = 3;
    PLATFORM_ASSEMBLY_TIME = 20; % 浮式平台组装时间更短

    % 初始化调度
    schedule = struct('turbine_id', [], 'vessel_id', [], ...
                     'start_time', [], 'end_time', [], ...
                     'process_id', [], 'berth_id', [], ...
                     'platform_id', []); % 添加浮式平台ID

    % 初始化船舶、泊位和浮式平台可用时间
    vessel_avail_time = zeros(1, vessel_count);
    berth_avail_time = zeros(1, berth_count);
    platform_avail_time = zeros(1, FLOATING_PLATFORM_COUNT);

    % 首先添加拼接时间（所有双体船需要拼接时间）
    welding_time = vessels(1).welding_time;
    for v = 1:vessel_count
        vessel_avail_time(v) = welding_time;
        % 添加拼接任务到调度
        schedule.turbine_id(end+1) = 0;
        schedule.vessel_id(end+1) = v;
        schedule.start_time(end+1) = 0;
        schedule.end_time(end+1) = welding_time;
        schedule.process_id(end+1) = -3; % -3表示拼接
        schedule.berth_id(end+1) = 0;
        schedule.platform_id(end+1) = 0;
    end

    % 计算港口到风场的航行时间
    port_to_farm_time = zeros(1, vessel_count);
    for v = 1:vessel_count
        vessel_speed = vessels(v).speed;
        port_to_farm_time(v) = 80 / vessel_speed;
    end

    % 使用优化的分配策略
    for i = 1:turbine_count
        turbine_idx = i;
        vessel_idx = mod(i-1, vessel_count) + 1; % 轮询分配

        % 装载阶段 - 只需要泊位进行装载
        [earliest_berth_time, berth_idx] = min(berth_avail_time);
        loading_start = max(vessel_avail_time(vessel_idx), earliest_berth_time);
        loading_end = loading_start + vessels(vessel_idx).loading_time;

        % 更新泊位可用时间（只用于装载）
        berth_avail_time(berth_idx) = loading_end;

        % 添加装载任务
        schedule.turbine_id(end+1) = turbine_idx;
        schedule.vessel_id(end+1) = vessel_idx;
        schedule.start_time(end+1) = loading_start;
        schedule.end_time(end+1) = loading_end;
        schedule.process_id(end+1) = 0; % 装载
        schedule.berth_id(end+1) = berth_idx;
        schedule.platform_id(end+1) = 0;

        if USE_FLOATING_PLATFORM
            % 前往浮式平台进行组装
            travel_to_platform_start = loading_end;
            travel_to_platform_end = travel_to_platform_start + port_to_farm_time(vessel_idx) * 0.3; % 浮式平台距离更近

            % 添加前往浮式平台任务
            schedule.turbine_id(end+1) = turbine_idx;
            schedule.vessel_id(end+1) = vessel_idx;
            schedule.start_time(end+1) = travel_to_platform_start;
            schedule.end_time(end+1) = travel_to_platform_end;
            schedule.process_id(end+1) = -4; % -4表示前往浮式平台
            schedule.berth_id(end+1) = 0;
            schedule.platform_id(end+1) = 0;

            % 浮式平台组装阶段 - 不受泊位限制
            [earliest_platform_time, platform_idx] = min(platform_avail_time);
            assembly_start = max(travel_to_platform_end, earliest_platform_time);
            assembly_end = assembly_start + PLATFORM_ASSEMBLY_TIME;

            % 更新浮式平台可用时间
            platform_avail_time(platform_idx) = assembly_end;

            % 添加浮式平台组装任务
            schedule.turbine_id(end+1) = turbine_idx;
            schedule.vessel_id(end+1) = vessel_idx;
            schedule.start_time(end+1) = assembly_start;
            schedule.end_time(end+1) = assembly_end;
            schedule.process_id(end+1) = 7; % 7表示浮式平台组装
            schedule.berth_id(end+1) = 0;
            schedule.platform_id(end+1) = platform_idx;

            % 前往风场进行最终安装
            travel_start = assembly_end;
            travel_end = travel_start + port_to_farm_time(vessel_idx) * 0.7; % 从浮式平台到风场
        else
            % 传统船上组装（受泊位限制）
            assembly_start = loading_end;
            assembly_end = assembly_start + vessels(vessel_idx).assembly_time;
            berth_avail_time(berth_idx) = assembly_end;

            schedule.turbine_id(end+1) = turbine_idx;
            schedule.vessel_id(end+1) = vessel_idx;
            schedule.start_time(end+1) = assembly_start;
            schedule.end_time(end+1) = assembly_end;
            schedule.process_id(end+1) = 5; % 船上组装
            schedule.berth_id(end+1) = berth_idx;
            schedule.platform_id(end+1) = 0;

            % 前往风场
            travel_start = assembly_end;
            travel_end = travel_start + port_to_farm_time(vessel_idx);
        end

        % 添加前往风场任务
        schedule.turbine_id(end+1) = turbine_idx;
        schedule.vessel_id(end+1) = vessel_idx;
        schedule.start_time(end+1) = travel_start;
        schedule.end_time(end+1) = travel_end;
        schedule.process_id(end+1) = -1; % 前往风场
        schedule.berth_id(end+1) = 0;
        schedule.platform_id(end+1) = 0;

        % 最终安装（替代所有常规工序）
        final_start = travel_end;
        final_end = final_start + vessels(vessel_idx).final_install_time;

        schedule.turbine_id(end+1) = turbine_idx;
        schedule.vessel_id(end+1) = vessel_idx;
        schedule.start_time(end+1) = final_start;
        schedule.end_time(end+1) = final_end;
        schedule.process_id(end+1) = 6; % 最终安装
        schedule.berth_id(end+1) = 0;
        schedule.platform_id(end+1) = 0;

        % 返回港口
        travel_back_start = final_end;
        travel_back_end = travel_back_start + port_to_farm_time(vessel_idx);

        schedule.turbine_id(end+1) = turbine_idx;
        schedule.vessel_id(end+1) = vessel_idx;
        schedule.start_time(end+1) = travel_back_start;
        schedule.end_time(end+1) = travel_back_end;
        schedule.process_id(end+1) = -2; % 返回港口
        schedule.berth_id(end+1) = 0;
        schedule.platform_id(end+1) = 0;

        % 更新船舶可用时间
        vessel_avail_time(vessel_idx) = travel_back_end;
    end

    % 如果没有platform_id字段，为兼容性添加
    if ~isfield(schedule, 'platform_id')
        schedule.platform_id = zeros(size(schedule.turbine_id));
    end
end

%% 双体船甘特图绘制函数（模仿单体船格式）
function plotDualVesselGantt(schedule, title_text, vessels, berths)
    % Create a professional-looking Gantt chart (same style as single vessel)
    figure('Position', [150, 150, 1200, 800], 'Color', 'white');

    % Check if the schedule is empty
    if isempty(schedule) || isempty(schedule.turbine_id)
        text(0.5, 0.5, '没有调度数据可显示', 'HorizontalAlignment', 'center', 'FontSize', 14);
        title(title_text, 'FontSize', 14, 'FontWeight', 'bold');
        return;
    end

    % Use the SAME color palette as single vessel
    process_colors = [
        0.9290, 0.6940, 0.3250;  % Loading (process 0) - soft orange
        0.4660, 0.7740, 0.8880;  % Travel to farm (process -1) - soft blue
        0.6350, 0.5040, 0.7410;  % Process 1 - soft purple (船上组装)
        0.4660, 0.7410, 0.3880;  % Process 2 - soft green (最终安装)
        0.8500, 0.3250, 0.3980;  % Process 3 - soft red
        0.9290, 0.6940, 0.1250;  % Process 4 - soft yellow
        0.4660, 0.7740, 0.8880;  % Return to port (process -2) - soft blue
        1.0000, 0.3000, 0.3000;  % Welding (process -3) - noticeable red
    ];

    % Process names for the legend (adapted for dual vessel with floating platform)
    process_names = {'装载', '前往风场', '船上组装', '最终安装', '前往平台', '浮式组装', '返回港口', '拼接焊接'};

    % Create dummy objects for the legend
    dummy_handles = zeros(1, length(process_names));
    for i = 1:length(process_names)
        dummy_handles(i) = plot(NaN, NaN, 'LineWidth', 8, 'Color', process_colors(i,:));
        hold on;
    end

    % Draw vessel timelines (same logic as single vessel)
    hold on;

    % Process tasks by vessel
    for v = 1:length(vessels)
        vessel_tasks = find(schedule.vessel_id == v);
        if isempty(vessel_tasks)
            continue;  % Skip if no tasks for this vessel
        end

        y_pos = v;

        % Sort tasks by start time to ensure proper sequence
        [~, sorted_idx] = sort(schedule.start_time(vessel_tasks));
        vessel_tasks = vessel_tasks(sorted_idx);

        for i = 1:length(vessel_tasks)
            task_idx = vessel_tasks(i);
            start_time = schedule.start_time(task_idx);
            end_time = schedule.end_time(task_idx);
            duration = end_time - start_time;

            % Skip zero-duration tasks
            if duration <= 0
                continue;
            end

            % Determine process type and color
            process_id = schedule.process_id(task_idx);

            % Map process_id to color index (adapted for dual vessel with floating platform)
            if process_id == 0      % Loading
                color_idx = 1;
                process_label = 'L';
            elseif process_id == -1  % Travel to farm
                color_idx = 2;
                process_label = 'TF';
            elseif process_id == -2  % Return to port
                color_idx = 7;
                process_label = 'TB';
            elseif process_id == -3  % Welding
                color_idx = 8;
                process_label = 'W';
            elseif process_id == -4  % Travel to platform
                color_idx = 5;
                process_label = 'TP';
            elseif process_id == 5   % Ship assembly
                color_idx = 3;
                process_label = 'SA';
            elseif process_id == 6   % Final installation
                color_idx = 4;
                process_label = 'FI';
            elseif process_id == 7   % Floating platform assembly
                color_idx = 6;
                process_label = 'FA';
            elseif process_id > 0   % Regular process (if any)
                color_idx = min(process_id + 2, size(process_colors, 1));
                process_label = ['P', num2str(process_id)];
            else                    % Handle unexpected cases
                color_idx = 1;      % Default to loading color
                process_label = '?';
            end

            % Create label (same format as single vessel)
            if schedule.turbine_id(task_idx) > 0
                label_text = sprintf('T%d-%s', schedule.turbine_id(task_idx), process_label);
            else
                % For welding tasks (turbine_id = 0)
                if process_id == -3
                    label_text = '拼接';
                else
                    label_text = process_label;
                end
            end

            % Draw task bar (same style as single vessel)
            rectangle('Position', [start_time, y_pos-0.4, duration, 0.8], ...
                'FaceColor', process_colors(color_idx,:), ...
                'EdgeColor', 'k', 'LineWidth', 0.5);

            % Adjust font size based on duration (same logic as single vessel)
            if duration > 30
                font_size = 8;
            elseif duration > 15
                font_size = 7;
            else
                font_size = 6;
            end

            % For very short durations, use rotated or external labels (same as single vessel)
            if duration < 5
                % External label with line
                text(start_time + duration/2, y_pos + 0.5, label_text, ...
                    'HorizontalAlignment', 'left', ...
                    'VerticalAlignment', 'bottom', ...
                    'FontSize', font_size, ...
                    'Rotation', 45);
                % Draw small line connecting to block
                line([start_time + duration/2, start_time + duration/2], [y_pos, y_pos + 0.4], 'Color', 'k', 'LineStyle', ':');
            else
                % Regular internal label
                text(start_time + duration/2, y_pos, label_text, ...
                    'HorizontalAlignment', 'center', ...
                    'VerticalAlignment', 'middle', ...
                    'FontSize', font_size);
            end
        end

        % Add vessel label on y-axis (same format as single vessel)
        text(-20, y_pos, sprintf('双船 %d', v), ...
            'HorizontalAlignment', 'right', 'FontWeight', 'bold');
    end

    % Add berth timelines (same logic as single vessel)
    berth_count = max(max(schedule.berth_id), 0);
    y_start = length(vessels) + 1;

    if berth_count > 0
        for b = 1:berth_count
            berth_tasks = find(schedule.berth_id == b);
            if isempty(berth_tasks)
                continue;  % Skip if no tasks for this berth
            end

            y_pos = y_start + b;

            % Sort by start time
            [~, sorted_idx] = sort(schedule.start_time(berth_tasks));
            berth_tasks = berth_tasks(sorted_idx);

            for i = 1:length(berth_tasks)
                task_idx = berth_tasks(i);

                % Skip welding tasks in berth display (they don't use berths)
                if schedule.process_id(task_idx) == -3
                    continue;
                end

                start_time = schedule.start_time(task_idx);
                end_time = schedule.end_time(task_idx);
                duration = end_time - start_time;

                % Skip zero-duration tasks
                if duration <= 0
                    continue;
                end

                % Determine color based on process
                process_id = schedule.process_id(task_idx);
                if process_id == 0      % Loading
                    color_idx = 1;
                    process_label = 'L';
                elseif process_id == 5  % Ship assembly
                    color_idx = 3;
                    process_label = 'SA';
                else
                    color_idx = 1;      % Default to loading color
                    process_label = 'L';
                end

                % Draw task bar
                rectangle('Position', [start_time, y_pos-0.4, duration, 0.8], ...
                    'FaceColor', process_colors(color_idx,:), ...
                    'EdgeColor', 'k', 'LineWidth', 0.5);

                % Adjust font size
                if duration > 30
                    font_size = 8;
                elseif duration > 15
                    font_size = 7;
                else
                    font_size = 6;
                end

                % Create label
                if schedule.turbine_id(task_idx) > 0
                    label_text = sprintf('T%d-%s', schedule.turbine_id(task_idx), process_label);
                else
                    label_text = process_label;
                end

                if duration < 5
                    % External label for small durations
                    text(start_time + duration/2, y_pos + 0.5, label_text, ...
                        'HorizontalAlignment', 'left', ...
                        'VerticalAlignment', 'bottom', ...
                        'FontSize', font_size, ...
                        'Rotation', 45);
                    line([start_time + duration/2, start_time + duration/2], [y_pos, y_pos + 0.4], 'Color', 'k', 'LineStyle', ':');
                else
                    % Regular internal label
                    text(start_time + duration/2, y_pos, label_text, ...
                        'HorizontalAlignment', 'center', ...
                        'VerticalAlignment', 'middle', ...
                        'FontSize', font_size);
                end
            end

            % Add berth label on y-axis
            text(-20, y_pos, sprintf('泊位 %d', b), ...
                'HorizontalAlignment', 'right', 'FontWeight', 'bold');
        end
    end

    % Add floating platform timelines
    if isfield(schedule, 'platform_id')
        platform_count = max(max(schedule.platform_id), 0);
        if platform_count > 0
            platform_y_start = y_start + berth_count + 1;
            for p = 1:platform_count
                platform_tasks = find(schedule.platform_id == p);
                if isempty(platform_tasks)
                    continue;  % Skip if no tasks for this platform
                end

                y_pos = platform_y_start + p;

                % Sort by start time
                [~, sorted_idx] = sort(schedule.start_time(platform_tasks));
                platform_tasks = platform_tasks(sorted_idx);

                for i = 1:length(platform_tasks)
                    task_idx = platform_tasks(i);

                    start_time = schedule.start_time(task_idx);
                    end_time = schedule.end_time(task_idx);
                    duration = end_time - start_time;

                    % Skip zero-duration tasks
                    if duration <= 0
                        continue;
                    end

                    % Floating platform assembly
                    color_idx = 6;
                    process_label = 'FA';

                    % Draw task bar
                    rectangle('Position', [start_time, y_pos-0.4, duration, 0.8], ...
                        'FaceColor', process_colors(color_idx,:), ...
                        'EdgeColor', 'k', 'LineWidth', 0.5);

                    % Adjust font size
                    if duration > 30
                        font_size = 8;
                    elseif duration > 15
                        font_size = 7;
                    else
                        font_size = 6;
                    end

                    % Create label
                    if schedule.turbine_id(task_idx) > 0
                        label_text = sprintf('T%d-%s', schedule.turbine_id(task_idx), process_label);
                    else
                        label_text = process_label;
                    end

                    if duration < 5
                        % External label for small durations
                        text(start_time + duration/2, y_pos + 0.5, label_text, ...
                            'HorizontalAlignment', 'left', ...
                            'VerticalAlignment', 'bottom', ...
                            'FontSize', font_size, ...
                            'Rotation', 45);
                        line([start_time + duration/2, start_time + duration/2], [y_pos, y_pos + 0.4], 'Color', 'k', 'LineStyle', ':');
                    else
                        % Regular internal label
                        text(start_time + duration/2, y_pos, label_text, ...
                            'HorizontalAlignment', 'center', ...
                            'VerticalAlignment', 'middle', ...
                            'FontSize', font_size);
                    end
                end

                % Add platform label on y-axis
                text(-20, y_pos, sprintf('浮台 %d', p), ...
                    'HorizontalAlignment', 'right', 'FontWeight', 'bold');
            end
        end
    end

    % Add legend with dummy handles (same as single vessel)
    legend(dummy_handles, process_names, 'Location', 'southoutside', 'Orientation', 'horizontal');

    % Set plot properties (same as single vessel)
    grid on;
    title(title_text, 'FontSize', 14, 'FontWeight', 'bold');
    xlabel('时间 (小时)', 'FontSize', 12);

    % Calculate and display makespan (same as single vessel)
    makespan = max(schedule.end_time);
    text(20, length(vessels) + berth_count + 2, ...
        sprintf('工期: %.2f 小时', makespan), ...
        'FontSize', 12, 'FontWeight', 'bold');

    % Set axis limits (same as single vessel)
    xlim([0, makespan * 1.05]);
    ylim([0, length(vessels) + max(berth_count, 1) + 3]);

    % Add clearer time grid lines (same as single vessel)
    set(gca, 'XGrid', 'on', 'XMinorGrid', 'on');
    set(gca, 'XTick', 0:20:ceil(makespan/20)*20);  % Major grid line every 20 hours
    set(gca, 'YTick', []);  % Remove y-tick labels as we have custom labels

    hold off;
end
%% 时间构成分析函数
function analyzeTimeBreakdown(single_schedule, dual_schedule, single_makespan, dual_makespan)
    fprintf('\n=== 详细时间构成分析 ===\n');

    % 分析单体船时间构成
    single_loading_time = sum((single_schedule.process_id == 0) .* (single_schedule.end_time - single_schedule.start_time));
    single_travel_time = sum((single_schedule.process_id == -1 | single_schedule.process_id == -2) .* (single_schedule.end_time - single_schedule.start_time));
    single_install_time = sum((single_schedule.process_id >= 1 & single_schedule.process_id <= 4) .* (single_schedule.end_time - single_schedule.start_time));

    fprintf('单体船时间构成：\n');
    fprintf('  装载时间：%.2f 小时 (%.1f%%)\n', single_loading_time, single_loading_time/single_makespan*100);
    fprintf('  航行时间：%.2f 小时 (%.1f%%)\n', single_travel_time, single_travel_time/single_makespan*100);
    fprintf('  安装时间：%.2f 小时 (%.1f%%)\n', single_install_time, single_install_time/single_makespan*100);

    % 分析双体船时间构成（包含浮式平台）
    dual_welding_time = sum((dual_schedule.process_id == -3) .* (dual_schedule.end_time - dual_schedule.start_time));
    dual_loading_time = sum((dual_schedule.process_id == 0) .* (dual_schedule.end_time - dual_schedule.start_time));
    dual_ship_assembly_time = sum((dual_schedule.process_id == 5) .* (dual_schedule.end_time - dual_schedule.start_time));
    dual_platform_assembly_time = sum((dual_schedule.process_id == 7) .* (dual_schedule.end_time - dual_schedule.start_time));
    dual_travel_time = sum((dual_schedule.process_id == -1 | dual_schedule.process_id == -2 | dual_schedule.process_id == -4) .* (dual_schedule.end_time - dual_schedule.start_time));
    dual_install_time = sum((dual_schedule.process_id == 6) .* (dual_schedule.end_time - dual_schedule.start_time));

    fprintf('\n双体船时间构成（含浮式平台）：\n');
    fprintf('  拼接时间：%.2f 小时 (%.1f%%)\n', dual_welding_time, dual_welding_time/dual_makespan*100);
    fprintf('  装载时间：%.2f 小时 (%.1f%%)\n', dual_loading_time, dual_loading_time/dual_makespan*100);
    if dual_ship_assembly_time > 0
        fprintf('  船上组装：%.2f 小时 (%.1f%%)\n', dual_ship_assembly_time, dual_ship_assembly_time/dual_makespan*100);
    end
    if dual_platform_assembly_time > 0
        fprintf('  浮台组装：%.2f 小时 (%.1f%%)\n', dual_platform_assembly_time, dual_platform_assembly_time/dual_makespan*100);
    end
    fprintf('  航行时间：%.2f 小时 (%.1f%%)\n', dual_travel_time, dual_travel_time/dual_makespan*100);
    fprintf('  最终安装：%.2f 小时 (%.1f%%)\n', dual_install_time, dual_install_time/dual_makespan*100);

    % 关键优势分析（包含浮式平台效果）
    fprintf('\n=== 关键技术优势分析 ===\n');
    install_efficiency = (single_install_time - dual_install_time) / single_install_time * 100;
    fprintf('海上安装时间节省：%.2f%% (%.2f → %.2f 小时)\n', ...
        install_efficiency, single_install_time, dual_install_time);

    total_assembly_time = dual_ship_assembly_time + dual_platform_assembly_time + dual_install_time;
    overall_install_efficiency = (single_install_time - total_assembly_time) / single_install_time * 100;
    fprintf('整体安装效率提升：%.2f%% (%.2f → %.2f 小时)\n', ...
        overall_install_efficiency, single_install_time, total_assembly_time);

    if dual_welding_time > 0
        roi = (single_install_time - dual_install_time) / dual_welding_time;
        fprintf('拼接时间投资回报：%.2f倍 (节省%.2f小时 vs 投入%.2f小时)\n', ...
            roi, single_install_time - dual_install_time, dual_welding_time);
    end

    % 浮式平台优势分析
    if dual_platform_assembly_time > 0
        fprintf('\n=== 浮式平台技术优势 ===\n');
        fprintf('✓ 摆脱泊位约束：浮式平台组装不占用港口泊位\n');
        fprintf('✓ 组装时间优化：%.0f小时 vs 传统船上组装25小时\n', dual_platform_assembly_time/40);
        fprintf('✓ 并行作业能力：3个浮式平台支持多船同时组装\n');
        platform_efficiency = (25 - dual_platform_assembly_time/40) / 25 * 100;
        fprintf('✓ 平台组装效率提升：%.1f%%\n', platform_efficiency);
    end

    % 规模效应分析
    fprintf('\n=== 规模效应分析 ===\n');
    fprintf('单台风机平均工期：\n');
    fprintf('  单体船：%.2f 小时/台\n', single_makespan / 40);
    fprintf('  双体船：%.2f 小时/台\n', dual_makespan / 40);
end

%% 泊位约束影响分析函数
function analyzeBerthConstraintImpact(single_schedule, dual_schedule, single_makespan, dual_makespan)
    fprintf('\n=== 泊位约束影响分析 ===\n');

    % 计算泊位利用率
    berth_count = 2; % 当前泊位数量

    % 单体船泊位利用分析
    single_berth_time = sum((single_schedule.berth_id > 0) .* (single_schedule.end_time - single_schedule.start_time));
    single_berth_utilization = single_berth_time / (berth_count * single_makespan) * 100;

    % 双体船泊位利用分析
    dual_berth_time = sum((dual_schedule.berth_id > 0) .* (dual_schedule.end_time - dual_schedule.start_time));
    dual_berth_utilization = dual_berth_time / (berth_count * dual_makespan) * 100;

    fprintf('泊位利用率分析：\n');
    fprintf('  单体船泊位利用率：%.1f%%\n', single_berth_utilization);
    fprintf('  双体船泊位利用率：%.1f%%\n', dual_berth_utilization);

    % 理论最优分析
    fprintf('\n=== 理论最优分析 ===\n');

    % 单体船理论最优（无泊位约束）
    single_avg_loading = mean((single_schedule.process_id == 0) .* (single_schedule.end_time - single_schedule.start_time));
    single_avg_travel = mean((single_schedule.process_id == -1 | single_schedule.process_id == -2) .* (single_schedule.end_time - single_schedule.start_time)) / 2;
    single_avg_install = sum((single_schedule.process_id >= 1 & single_schedule.process_id <= 4) .* (single_schedule.end_time - single_schedule.start_time)) / 40;
    single_theoretical = (single_avg_loading + single_avg_travel*2 + single_avg_install) * 40 / 10;

    % 双体船理论最优（无泊位约束）
    dual_avg_loading = mean((dual_schedule.process_id == 0) .* (dual_schedule.end_time - dual_schedule.start_time));
    dual_avg_assembly = mean((dual_schedule.process_id == 5) .* (dual_schedule.end_time - dual_schedule.start_time));
    dual_avg_travel = mean((dual_schedule.process_id == -1 | dual_schedule.process_id == -2) .* (dual_schedule.end_time - dual_schedule.start_time)) / 2;
    dual_avg_install = mean((dual_schedule.process_id == 6) .* (dual_schedule.end_time - dual_schedule.start_time));
    dual_welding = mean((dual_schedule.process_id == -3) .* (dual_schedule.end_time - dual_schedule.start_time));
    dual_theoretical = dual_welding + (dual_avg_loading + dual_avg_assembly + dual_avg_travel*2 + dual_avg_install) * 40 / 5;

    fprintf('理论最优工期（无泊位约束）：\n');
    fprintf('  单体船理论最优：%.2f 小时\n', single_theoretical);
    fprintf('  双体船理论最优：%.2f 小时\n', dual_theoretical);

    % 泊位约束损失
    single_berth_loss = (single_makespan - single_theoretical) / single_theoretical * 100;
    dual_berth_loss = (dual_makespan - dual_theoretical) / dual_theoretical * 100;

    fprintf('\n泊位约束造成的效率损失：\n');
    fprintf('  单体船效率损失：%.1f%% (%.2f → %.2f 小时)\n', single_berth_loss, single_theoretical, single_makespan);
    fprintf('  双体船效率损失：%.1f%% (%.2f → %.2f 小时)\n', dual_berth_loss, dual_theoretical, dual_makespan);

    % 双体船真正优势
    if dual_theoretical < single_theoretical
        theoretical_advantage = (single_theoretical - dual_theoretical) / single_theoretical * 100;
        fprintf('\n✓ 双体船理论优势：%.1f%% (%.2f vs %.2f 小时)\n', theoretical_advantage, dual_theoretical, single_theoretical);
        fprintf('✓ 双体船在充足泊位条件下具有显著优势！\n');
    else
        fprintf('\n✗ 当前参数下单体船理论上仍更优\n');
    end

    % 关键瓶颈识别
    fprintf('\n=== 关键瓶颈识别 ===\n');
    if dual_berth_loss > single_berth_loss
        fprintf('⚠️  双体船受泊位约束影响更严重\n');
        fprintf('⚠️  建议增加泊位数量或优化港口作业流程\n');
    end

    if dual_makespan > single_makespan && dual_theoretical < single_theoretical
        fprintf('⚠️  双体船技术优势被泊位约束掩盖\n');
        fprintf('⚠️  需要港口基础设施配套升级\n');
    end
end



function plotGanttChart(schedule, title_text, turbines, vessels, disruption)
    % Create a professional-looking Gantt chart
    figure('Position', [100, 100, 1200, 800], 'Color', 'white');
    
    % Check if the schedule is empty
    if isempty(schedule) || isempty(schedule.turbine_id)
        text(0.5, 0.5, '没有调度数据可显示', 'HorizontalAlignment', 'center', 'FontSize', 14);
        title(title_text, 'FontSize', 14, 'FontWeight', 'bold');
        return;
    end
    
    % Use a softer color palette
    process_colors = [
        0.9290, 0.6940, 0.3250;  % Loading (process 0) - soft orange
        0.4660, 0.7740, 0.8880;  % Travel to farm (process -1) - soft blue
        0.6350, 0.5040, 0.7410;  % Process 1 - soft purple
        0.4660, 0.7410, 0.3880;  % Process 2 - soft green
        0.8500, 0.3250, 0.3980;  % Process 3 - soft red
        0.9290, 0.6940, 0.1250;  % Process 4 - soft yellow
        0.4660, 0.7740, 0.8880;  % Return to port (process -2) - soft blue
        1.0000, 0.3000, 0.3000;  % Repair (process -3) - noticeable but not too bright red
    ];
    
    % Process names for the legend
    process_names = {'装载', '前往风场', '工序 1', '工序 2', '工序 3', '工序 4', '返回港口', '维修'};
    
    % Create dummy objects for the legend
    dummy_handles = zeros(1, length(process_names));
    for i = 1:length(process_names)
        dummy_handles(i) = plot(NaN, NaN, 'LineWidth', 8, 'Color', process_colors(i,:));
        hold on;
    end
    
    % Draw vessel timelines
    hold on;
    
    % If there is a disruption, add a disruption marker line
    if ~isempty(disruption) && isstruct(disruption) && isfield(disruption, 'time')
        % Draw a vertical line at the time of disruption
        line([disruption.time, disruption.time], [0, length(vessels)+3], ...
            'Color', 'r', 'LineStyle', '--', 'LineWidth', 1.5);
        
        % Add disruption marker text
        text(disruption.time, length(vessels)+2.5, '故障发生', ...
            'HorizontalAlignment', 'center', 'Color', 'r', ...
            'FontWeight', 'bold', 'VerticalAlignment', 'bottom');
    end
    
    % Pre-identify repair tasks
    repair_indices = [];
    affected_vessel = -1;
    
    if ~isempty(disruption) && isstruct(disruption)
        % Try to find repair tasks based on turbine_id = 0 (no specific turbine)
        repair_by_turbine = find(schedule.turbine_id == 0);
        
        % Try to find repair tasks based on process_id = -3
        repair_by_process = find(schedule.process_id == -3);
        
        % Combine the results, prioritizing process_id = -3
        if ~isempty(repair_by_process)
            repair_indices = repair_by_process;
        elseif ~isempty(repair_by_turbine)
            repair_indices = repair_by_turbine;
        end
        
        % If we have a disruption structure with affected_vessel field
        if isfield(disruption, 'affected_vessel') && ~isempty(disruption.affected_vessel)
            affected_vessel = disruption.affected_vessel;
            
            % If we still don't have repair tasks, try to find them by vessel and time
            if isempty(repair_indices) && affected_vessel > 0
                % Find tasks for the affected vessel near the disruption time
                potential_repair = find(schedule.vessel_id == affected_vessel & ...
                                       abs(schedule.start_time - disruption.time) < 5);
                if ~isempty(potential_repair)
                    repair_indices = [repair_indices, potential_repair];
                    repair_indices = unique(repair_indices); % Remove duplicates
                end
            end
        end
    end
    
    % Process tasks by vessel
    for v = 1:length(vessels)
        vessel_tasks = find(schedule.vessel_id == v);
        if isempty(vessel_tasks)
            continue;  % Skip if no tasks for this vessel
        end
        
        y_pos = v;
        
        % Sort tasks by start time to ensure proper sequence
        [~, sorted_idx] = sort(schedule.start_time(vessel_tasks));
        vessel_tasks = vessel_tasks(sorted_idx);
        
        for i = 1:length(vessel_tasks)
            task_idx = vessel_tasks(i);
            start_time = schedule.start_time(task_idx);
            end_time = schedule.end_time(task_idx);
            duration = end_time - start_time;
            
            % Skip zero-duration tasks
            if duration <= 0
                continue;
            end
            
            % Determine if this is a repair task using multiple criteria
            is_repair = false;
            
            % Check if this task index is in our pre-identified repair indices
            if ~isempty(repair_indices) && ismember(task_idx, repair_indices)
                is_repair = true;
            end
            
            % Check if process_id indicates repair
            if schedule.process_id(task_idx) == -3
                is_repair = true;
            end
            
            % Check if this is a zero turbine_id task for affected vessel
            if schedule.turbine_id(task_idx) == 0 && v == affected_vessel
                is_repair = true;
            end
            
            % Check if this task starts at or very near the disruption time on the affected vessel
            if ~isempty(disruption) && isfield(disruption, 'time') && ...
               v == affected_vessel && abs(start_time - disruption.time) < 2
                is_repair = true;
            end
            
            % Set color and label based on task type
            if is_repair
                % Force this task to be marked as repair
                color_idx = 8;  % Repair color
                label_text = '维修';
            else
                % Determine regular process type
                process_id = schedule.process_id(task_idx);
                
                % Map process_id to color index
                if process_id == 0      % Loading
                    color_idx = 1;
                    process_label = 'L';
                elseif process_id == -1  % Travel to farm
                    color_idx = 2;
                    process_label = 'TF';
                elseif process_id == -2  % Return to port
                    color_idx = 7;
                    process_label = 'TB';
                elseif process_id > 0   % Regular process
                    color_idx = min(process_id + 2, size(process_colors, 1)); % Prevent index out of bounds
                    process_label = ['P', num2str(process_id)];
                else                    % Handle unexpected cases
                    color_idx = 1;      % Default to loading color
                    process_label = '?';
                end
                
                % Create label
                if schedule.turbine_id(task_idx) > 0
                    label_text = sprintf('T%d-%s', schedule.turbine_id(task_idx), process_label);
                else
                    % If turbine_id is 0 but not repair, just show process label
                    label_text = process_label;
                end
            end
            
            % Draw task bar
            rectangle('Position', [start_time, y_pos-0.4, duration, 0.8], ...
                'FaceColor', process_colors(color_idx,:), ...
                'EdgeColor', 'k', 'LineWidth', 0.5);
            
            % Adjust font size based on duration
            if duration > 30
                font_size = 8;
            elseif duration > 15
                font_size = 7;
            else
                font_size = 6;
            end
            
            % For very short durations, use rotated or external labels
            if duration < 5
                % External label with line
                text(start_time + duration/2, y_pos + 0.5, label_text, ...
                    'HorizontalAlignment', 'left', ...
                    'VerticalAlignment', 'bottom', ...
                    'FontSize', font_size, ...
                    'Rotation', 45);
                % Draw small line connecting to block
                line([start_time + duration/2, start_time + duration/2], [y_pos, y_pos + 0.4], 'Color', 'k', 'LineStyle', ':');
            else
                % Regular internal label
                text(start_time + duration/2, y_pos, label_text, ...
                    'HorizontalAlignment', 'center', ...
                    'VerticalAlignment', 'middle', ...
                    'FontSize', font_size);
            end
        end
        
        % Add vessel label on y-axis
        % If this is the affected vessel, add marker
        if v == affected_vessel
            text(-20, y_pos, sprintf('船舶 %d (故障)', v), ...
                'HorizontalAlignment', 'right', 'FontWeight', 'bold', 'Color', 'r');
        else
            text(-20, y_pos, sprintf('船舶 %d', v), ...
                'HorizontalAlignment', 'right', 'FontWeight', 'bold');
        end
    end
    
    % Add berth timelines - ensure no repair tasks are displayed
    berth_count = max(max(schedule.berth_id), 0);
    if berth_count > 0
        y_start = length(vessels) + 1;
        for b = 1:berth_count
            berth_tasks = find(schedule.berth_id == b);
            if isempty(berth_tasks)
                continue;  % Skip if no tasks for this berth
            end
            
            y_pos = y_start + b;
            
            % Sort by start time
            [~, sorted_idx] = sort(schedule.start_time(berth_tasks));
            berth_tasks = berth_tasks(sorted_idx);
            
            for i = 1:length(berth_tasks)
                task_idx = berth_tasks(i);
                
                % Skip repair tasks in berth display
                if ~isempty(repair_indices) && ismember(task_idx, repair_indices)
                    continue;
                end
                
                if schedule.process_id(task_idx) == -3
                    continue;
                end
                
                if schedule.turbine_id(task_idx) == 0 && ...
                   (schedule.vessel_id(task_idx) == affected_vessel || ...
                    (~isempty(disruption) && isfield(disruption, 'time') && ...
                    abs(schedule.start_time(task_idx) - disruption.time) < 2))
                    continue;
                end
                
                start_time = schedule.start_time(task_idx);
                end_time = schedule.end_time(task_idx);
                duration = end_time - start_time;
                
                % Skip zero-duration tasks
                if duration <= 0
                    continue;
                end
                
                % Draw task bar (loading is always process_id 0)
                rectangle('Position', [start_time, y_pos-0.4, duration, 0.8], ...
                    'FaceColor', process_colors(1,:), ...
                    'EdgeColor', 'k', 'LineWidth', 0.5);
                
                % Adjust font size
                if duration > 30
                    font_size = 8;
                elseif duration > 15
                    font_size = 7;
                else
                    font_size = 6;
                end
                
                % Create label
                if schedule.turbine_id(task_idx) > 0
                    label_text = sprintf('T%d-L', schedule.turbine_id(task_idx));
                else
                    label_text = 'L';
                end
                
                if duration < 5
                    % External label for small durations
                    text(start_time + duration/2, y_pos + 0.5, label_text, ...
                        'HorizontalAlignment', 'left', ...
                        'VerticalAlignment', 'bottom', ...
                        'FontSize', font_size, ...
                        'Rotation', 45);
                    line([start_time + duration/2, start_time + duration/2], [y_pos, y_pos + 0.4], 'Color', 'k', 'LineStyle', ':');
                else
                    % Regular internal label
                    text(start_time + duration/2, y_pos, label_text, ...
                        'HorizontalAlignment', 'center', ...
                        'VerticalAlignment', 'middle', ...
                        'FontSize', font_size);
                end
            end
            
            % Add berth label on y-axis
            text(-20, y_pos, sprintf('泊位 %d', b), ...
                'HorizontalAlignment', 'right', 'FontWeight', 'bold');
        end
    end
    
    % Add legend with dummy handles
    legend(dummy_handles, process_names, 'Location', 'southoutside', 'Orientation', 'horizontal');
    
    % Set plot properties
    grid on;
    title(title_text, 'FontSize', 14, 'FontWeight', 'bold');
    xlabel('时间 (小时)', 'FontSize', 12);
    % No y-axis label needed as we have custom labels
    
    % Calculate and display makespan
    makespan = max(schedule.end_time);
    text(20, length(vessels) + berth_count + 2, ...
        sprintf('工期: %.2f 小时', makespan), ...
        'FontSize', 12, 'FontWeight', 'bold');
    
    % Set axis limits - corrected part to ensure display of critical time range
    % Find critical time points
    if ~isempty(disruption) && isfield(disruption, 'time')
        % Show tasks before and after disruption
        start_view = max(0, disruption.time - 100); % Show 100 hours before disruption
        end_view = makespan * 1.05;
        xlim([start_view, end_view]);
    else
        % For initial schedule, maintain original view
        xlim([0, makespan * 1.05]);
    end
    
    % Ensure reasonable y-axis range
    ylim([0, length(vessels) + max(berth_count, 1) + 3]);
    
    % Add clearer time grid lines
    set(gca, 'XGrid', 'on', 'XMinorGrid', 'on');
    if ~isempty(disruption) && isfield(disruption, 'time')
        % Add grid lines near disruption time
        set(gca, 'XTick', (floor(disruption.time/20)-5)*20:20:ceil(makespan/20)*20);
    else
        set(gca, 'XTick', 0:20:ceil(makespan/20)*20);  % Major grid line every 20 hours
    end
    
    set(gca, 'YTick', []);  % Remove y-tick labels as we have custom labels
    
    hold off;
end