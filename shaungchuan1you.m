%% Hybrid GA-PSO Algorithm for Offshore Wind Turbine Installation Scheduling
% This code implements a hybrid GA-PSO algorithm for scheduling offshore wind
% turbine installation tasks with disruption handling and rescheduling.
% Enhanced with dual-hull vessel method and sea condition considerations.

clear all;
close all;
clc;
rng(42); % For reproducibility

%% Problem Parameters
TURBINE_COUNT = 40;        % Wind turbine count
VESSEL_COUNT = 10;         % Vessel count
BERTH_COUNT = 2;           % Berth count
% Process times: [foundation installation, tower installation, nacelle installation, blade installation]
PROCESS_TIMES = [20, 12, 10, 36]; % Process times (hours) for conventional installation
% Ship assembly times for different assembly levels
SHIP_ASSEMBLY_TIMES = struct('partial', 25, 'complete', 40); % Hours for ship assembly
% Final installation time for completely assembled turbines
FINAL_INSTALLATION_TIME = 15; % Hours for final installation of completely assembled turbines
MAX_CAPACITY = 4;          % Max vessel capacity (turbines)

% Distance and time parameters
global PORT_TO_FARM_DISTANCE SEA_CONDITIONS FINAL_INSTALLATION_TIME; % Define as global so functions can access it
PORT_TO_FARM_DISTANCE = 80; % Port to farm distance (km)
VESSEL_SPEEDS = ones(1, VESSEL_COUNT) * 15; % Vessel speeds (km/h)
VESSEL_SPEEDS = VESSEL_SPEEDS .* (0.8 + 0.4*rand(1, VESSEL_COUNT)); % Random variation
TURBINE_DISTANCE = 1;      % Turbine distance (km)
LOADING_TIMES = 6 * ones(1, VESSEL_COUNT); % Base loading time
LOADING_TIMES = LOADING_TIMES .* (0.8 + 0.4*rand(1, VESSEL_COUNT)); % Random variation

% Sea condition parameters
global SEA_CONDITIONS;
SEA_CONDITIONS = struct('wave_height', [], 'wind_speed', [], 'current_speed', []);
% Generate random sea conditions (significant wave height in meters)
SEA_CONDITIONS.wave_height = 0.5 + 3.5 * rand(1, 500); % 0.5-4m wave height
% Smooth the wave heights to create realistic patterns
SEA_CONDITIONS.wave_height = smoothdata(SEA_CONDITIONS.wave_height, 'gaussian', 10);
% Wind speed (m/s)
SEA_CONDITIONS.wind_speed = 2 + 13 * rand(1, 500); % 2-15 m/s wind speed
SEA_CONDITIONS.wind_speed = smoothdata(SEA_CONDITIONS.wind_speed, 'gaussian', 10);
% Current speed (knots)
SEA_CONDITIONS.current_speed = 0.2 + 1.8 * rand(1, 500); % 0.2-2 knots current
SEA_CONDITIONS.current_speed = smoothdata(SEA_CONDITIONS.current_speed, 'gaussian', 10);

% Algorithm parameters
global POP_SIZE MAX_GEN PSO_ITERATIONS CROSSOVER_RATE MUTATION_RATE_GA...
      MUTATION_RATE_PSO INERTIA_WEIGHT C1 C2 DIVERSITY_THRESHOLD;
POP_SIZE = 50;            % Population size
MAX_GEN = 50;             % Maximum generations
PSO_ITERATIONS = 50;      % PSO iterations
CROSSOVER_RATE = 0.8;     % Crossover rate
MUTATION_RATE_GA = 0.01;  % GA mutation rate
MUTATION_RATE_PSO = 0.19; % PSO mutation rate
INERTIA_WEIGHT = 0.78;    % Inertia weight
C1 = 1.4;                 % Cognitive parameter
C2 = 1.5;                 % Social parameter
DIVERSITY_THRESHOLD = 0.05; % Diversity threshold for PSO intervention

% Disruption parameters
DISRUPTION_TIME = 154;    % Time of disruption (hours) - changed to occur earlier
REPAIR_TIME = 52;         % Repair time (hours)
AFFECTED_VESSEL = 5;      % Affected vessel ID

%% Data Structures
% Define turbine installation tasks with enhanced properties
turbines = struct('id', num2cell(1:TURBINE_COUNT), ...
                 'processes', cell(1, TURBINE_COUNT), ...
                 'assembly_level', cell(1, TURBINE_COUNT), ... % 0=none, 1=partial, 2=complete
                 'ship_assembly_time', cell(1, TURBINE_COUNT), ...
                 'sea_condition_index', cell(1, TURBINE_COUNT), ...
                 'preassembly_time', cell(1, TURBINE_COUNT), ...
                 'is_preassembled', cell(1, TURBINE_COUNT));

% Initialize process times for each turbine
for i = 1:TURBINE_COUNT
    % Add slight variations to process times (±10%)
    variation = 0.9 + 0.2*rand(1, length(PROCESS_TIMES));
    turbines(i).processes = PROCESS_TIMES .* variation;

    % Initialize assembly level to 0 (no assembly)
    % This will be set based on vessel assignment later
    turbines(i).assembly_level = 0;    % Initialize ship assembly times for different levels
    % These will be used if the turbine is assigned to a vessel that can do assembly
    turbines(i).ship_assembly_time = struct(...
        'partial', SHIP_ASSEMBLY_TIMES.partial * (0.9 + 0.2*rand()), ...
        'complete', SHIP_ASSEMBLY_TIMES.complete * (0.9 + 0.2*rand()));

    % Initialize preassembly time (default to 0, will be set during scheduling)
    turbines(i).preassembly_time = 0;

    % Initialize is_preassembled flag (default to false)
    turbines(i).is_preassembled = false;

    % Assign random sea condition index for this turbine's location
    % This will be used to look up sea conditions during installation
    turbines(i).sea_condition_index = randi([1, length(SEA_CONDITIONS.wave_height)]);
end

% Define vessels with type (single-hull or dual-hull)
% Randomly assign vessel types: 1 for single-hull, 2 for dual-hull
VESSEL_TYPES = randi([1, 2], 1, VESSEL_COUNT);
% Ensure we have at least 3 of each type
if sum(VESSEL_TYPES == 1) < 3
    idx = find(VESSEL_TYPES == 2);
    VESSEL_TYPES(idx(1:3)) = 1;
elseif sum(VESSEL_TYPES == 2) < 3
    idx = find(VESSEL_TYPES == 1);
    VESSEL_TYPES(idx(1:3)) = 2;
end

% Define vessel properties based on type
DECK_SPACE = zeros(1, VESSEL_COUNT);
STABILITY = zeros(1, VESSEL_COUNT);
SEA_CONDITION_EFFICIENCY = zeros(1, VESSEL_COUNT);
MAX_ASSEMBLY_LEVEL = zeros(1, VESSEL_COUNT);

for i = 1:VESSEL_COUNT
    if VESSEL_TYPES(i) == 1 % Single-hull vessel
        DECK_SPACE(i) = 1.0 + 0.2 * rand(); % Base deck space
        STABILITY(i) = 0.4 + 0.3 * rand(); % 0.4-0.7 stability
        SEA_CONDITION_EFFICIENCY(i) = 0.5 + 0.3 * rand(); % 0.5-0.8 efficiency
        MAX_ASSEMBLY_LEVEL(i) = 1; % Can only do partial assembly
    else % Dual-hull vessel
        DECK_SPACE(i) = 2.5 + 0.5 * rand(); % 2.5-3x deck space
        STABILITY(i) = 0.7 + 0.25 * rand(); % 0.7-0.95 stability
        SEA_CONDITION_EFFICIENCY(i) = 0.7 + 0.25 * rand(); % 0.7-0.95 efficiency
        MAX_ASSEMBLY_LEVEL(i) = 2; % Can do complete assembly
    end
end

% Define vessels with enhanced properties
vessels = struct('id', num2cell(1:VESSEL_COUNT), ...
                'type', num2cell(VESSEL_TYPES), ... % 1: single-hull, 2: dual-hull
                'speed', num2cell(VESSEL_SPEEDS), ...
                'capacity', num2cell(ones(1, VESSEL_COUNT) * MAX_CAPACITY), ...
                'loading_time', num2cell(LOADING_TIMES), ...
                'deck_space', num2cell(DECK_SPACE), ... % Deck space for assembly
                'stability', num2cell(STABILITY), ... % Stability in rough seas
                'sea_efficiency', num2cell(SEA_CONDITION_EFFICIENCY), ... % Efficiency in rough seas
                'max_assembly_level', num2cell(MAX_ASSEMBLY_LEVEL)); % Maximum possible assembly level

% Define berths
berths = struct('id', num2cell(1:BERTH_COUNT));

%% Hybrid GA-PSO Algorithm for Initial Schedule
fprintf('Generating initial schedule using Hybrid GA-PSO algorithm...\n');
initial_schedule = hybridGAPSO(turbines, vessels, berths);
makespan_initial = calculateMakespan(initial_schedule);
fprintf('Initial schedule makespan: %.2f hours\n', makespan_initial);

%% Generate Disruption
fprintf('Simulating disruption at time %.2f hours for vessel %d...\n', ...
    DISRUPTION_TIME, AFFECTED_VESSEL);
disruption = struct('time', DISRUPTION_TIME, ...
                    'repair_time', REPAIR_TIME, ...
                    'affected_vessel', AFFECTED_VESSEL);

%% 注：完全重调度方法已从此代码中移除，只保留右移调度方法

%% Right-Shift Rescheduling
fprintf('Applying right-shift rescheduling...\n');
right_shift_schedule = rightShiftRescheduling(initial_schedule, disruption);
makespan_right_shift = calculateMakespan(right_shift_schedule);
fprintf('Right-shift schedule makespan: %.2f hours\n', makespan_right_shift);

%% Plot Results
% Plot Gantt charts
plotGanttChart(initial_schedule, '初始调度计划', turbines, vessels, []);
plotGanttChart(right_shift_schedule, '右移重调度计划', turbines, vessels, disruption);

% Plot convergence curve if available
if exist('convergence_curve', 'var')
    figure;
    plot(convergence_curve, 'LineWidth', 2);
    xlabel('Generation');
    ylabel('Best Makespan (hours)');
    title('Convergence Curve of Hybrid GA-PSO Algorithm');
    grid on;
end

%% Hybrid GA-PSO Algorithm Function
function schedule = hybridGAPSO(turbines, vessels, berths)
    % Access global variables
    global POP_SIZE MAX_GEN PSO_ITERATIONS CROSSOVER_RATE MUTATION_RATE_GA...
           MUTATION_RATE_PSO INERTIA_WEIGHT C1 C2 DIVERSITY_THRESHOLD;

    % Define if not in global scope
    if isempty(POP_SIZE), POP_SIZE = 50; end
    if isempty(MAX_GEN), MAX_GEN = 200; end
    if isempty(PSO_ITERATIONS), PSO_ITERATIONS = 50; end
    if isempty(CROSSOVER_RATE), CROSSOVER_RATE = 0.8; end
    if isempty(MUTATION_RATE_GA), MUTATION_RATE_GA = 0.01; end
    if isempty(MUTATION_RATE_PSO), MUTATION_RATE_PSO = 0.2; end
    if isempty(INERTIA_WEIGHT), INERTIA_WEIGHT = 0.8; end
    if isempty(C1), C1 = 1.5; end
    if isempty(C2), C2 = 1.5; end
    if isempty(DIVERSITY_THRESHOLD), DIVERSITY_THRESHOLD = 0.05; end

    % Get problem dimensions
    turbine_count = length(turbines);
    vessel_count = length(vessels);

    % Initialize population - Each individual is a permutation of turbines
    % and assignment of vessels to turbines
    population = cell(1, POP_SIZE);
    fitness = zeros(1, POP_SIZE);

    % Generate initial population
    for i = 1:POP_SIZE
        % Random permutation of turbines
        turbine_order = randperm(turbine_count);
        % Random vessel assignment
        vessel_assignment = randi(vessel_count, 1, turbine_count);
        population{i} = struct('turbine_order', turbine_order, ...
                              'vessel_assignment', vessel_assignment);
    end

    % Initialize PSO parameters
    pbest = population;
    pbest_fitness = Inf(1, POP_SIZE);
    gbest = [];
    gbest_fitness = Inf;
    velocity = cell(1, POP_SIZE);

    % Initialize velocities
    for i = 1:POP_SIZE
        velocity{i} = struct('turbine_order', zeros(1, turbine_count), ...
                            'vessel_assignment', zeros(1, turbine_count));
    end

    % Convergence tracking
    convergence_curve = zeros(1, MAX_GEN);

    % Main loop
    for gen = 1:MAX_GEN
        % Evaluate population
        for i = 1:POP_SIZE
            % Convert chromosome to schedule
            schedule_i = decodeChromosome(population{i}, turbines, vessels, berths);
            % Calculate makespan
            fitness(i) = calculateMakespan(schedule_i);

            % Update personal best
            if fitness(i) < pbest_fitness(i)
                pbest{i} = population{i};
                pbest_fitness(i) = fitness(i);
            end
        end

        % Update global best
        [min_fitness, min_idx] = min(fitness);
        if min_fitness < gbest_fitness
            gbest = population{min_idx};
            gbest_fitness = min_fitness;
        end

        % Store best fitness for convergence curve
        convergence_curve(gen) = gbest_fitness;

        % Display progress
        if mod(gen, 10) == 0
            fprintf('Generation %d: Best makespan = %.2f hours\n', gen, gbest_fitness);
        end

        % Check diversity
        diversity = calculateDiversity(fitness);

        % Apply PSO if diversity is below threshold
        if diversity < DIVERSITY_THRESHOLD
            fprintf('Low diversity detected at generation %d. Applying PSO...\n', gen);

            % Apply PSO for some iterations
            for pso_iter = 1:PSO_ITERATIONS
                for i = 1:POP_SIZE
                    % Update velocity
                    velocity{i}.turbine_order = INERTIA_WEIGHT * velocity{i}.turbine_order + ...
                        C1 * rand() * (pbest{i}.turbine_order - population{i}.turbine_order) + ...
                        C2 * rand() * (gbest.turbine_order - population{i}.turbine_order);

                    velocity{i}.vessel_assignment = INERTIA_WEIGHT * velocity{i}.vessel_assignment + ...
                        C1 * rand() * (pbest{i}.vessel_assignment - population{i}.vessel_assignment) + ...
                        C2 * rand() * (gbest.vessel_assignment - population{i}.vessel_assignment);

                    % Update position
                    % For turbine order (permutation), we use a special approach
                    new_turbine_order = population{i}.turbine_order + round(velocity{i}.turbine_order);
                    % Ensure it remains a valid permutation
                    [~, new_turbine_order] = sort(new_turbine_order);

                    % For vessel assignment
                    new_vessel_assignment = population{i}.vessel_assignment + round(velocity{i}.vessel_assignment);
                    % Ensure it remains valid (between 1 and vessel_count)
                    new_vessel_assignment = max(1, min(vessel_count, new_vessel_assignment));

                    % Apply mutation with higher rate
                    if rand() < MUTATION_RATE_PSO
                        % Swap two positions in turbine order
                        idx = randperm(turbine_count, 2);
                        temp = new_turbine_order(idx(1));
                        new_turbine_order(idx(1)) = new_turbine_order(idx(2));
                        new_turbine_order(idx(2)) = temp;
                    end

                    % Update population
                    population{i}.turbine_order = new_turbine_order;
                    population{i}.vessel_assignment = new_vessel_assignment;

                    % Evaluate new solution
                    schedule_i = decodeChromosome(population{i}, turbines, vessels, berths);
                    fitness(i) = calculateMakespan(schedule_i);

                    % Update personal best
                    if fitness(i) < pbest_fitness(i)
                        pbest{i} = population{i};
                        pbest_fitness(i) = fitness(i);
                    end
                end

                % Update global best
                [min_fitness, min_idx] = min(fitness);
                if min_fitness < gbest_fitness
                    gbest = population{min_idx};
                    gbest_fitness = min_fitness;
                end
            end
        else
            % GA operations (selection, crossover, mutation)
            new_population = cell(1, POP_SIZE);

            % Elitism - keep best solution
            [~, elite_idx] = min(fitness);
            new_population{1} = population{elite_idx};

            % Selection, crossover and mutation for the rest
            for i = 2:POP_SIZE
                % Tournament selection
                tour_idx = randperm(POP_SIZE, 2);
                if fitness(tour_idx(1)) < fitness(tour_idx(2))
                    parent1 = population{tour_idx(1)};
                else
                    parent1 = population{tour_idx(2)};
                end

                tour_idx = randperm(POP_SIZE, 2);
                if fitness(tour_idx(1)) < fitness(tour_idx(2))
                    parent2 = population{tour_idx(1)};
                else
                    parent2 = population{tour_idx(2)};
                end

                % Crossover
                if rand() < CROSSOVER_RATE
                    child = crossover(parent1, parent2, turbine_count);
                else
                    child = parent1;
                end

                % Mutation
                if rand() < MUTATION_RATE_GA
                    child = mutate(child, vessel_count, turbine_count);
                end

                new_population{i} = child;
            end

            population = new_population;
        end

        % Check termination criteria
        if gen >= MAX_GEN
            break;
        end
    end

    % Convert best solution to schedule
    schedule = decodeChromosome(gbest, turbines, vessels, berths);

    % Store convergence curve in global variable for plotting
    assignin('base', 'convergence_curve', convergence_curve);
end

%% Helper Functions
function schedule = decodeChromosome(chromosome, turbines, vessels, berths)
    % Access the global variables for distance, sea conditions, and installation parameters
    global PORT_TO_FARM_DISTANCE SEA_CONDITIONS FINAL_INSTALLATION_TIME;

    % Decodes chromosome to a feasible schedule
    turbine_order = chromosome.turbine_order;
    vessel_assignment = chromosome.vessel_assignment;

    % Initialize schedule data structure
    turbine_count = length(turbines);
    vessel_count = length(vessels);
    berth_count = length(berths);

    % Initialize vessel and berth availability times
    vessel_avail_time = zeros(1, vessel_count);
    berth_avail_time = zeros(1, berth_count);

    % Initialize schedule
    schedule = struct('turbine_id', [], 'vessel_id', [], ...
                     'start_time', [], 'end_time', [], ...
                     'process_id', [], 'berth_id', [], ...
                     'sea_condition', [], 'assembly_level', []);

    % Port to farm travel time for each vessel
    port_to_farm_time = zeros(1, vessel_count);
    for v = 1:vessel_count
        % Extract vessel speed as scalar
        vessel_speed = vessels(v).speed;
        port_to_farm_time(v) = PORT_TO_FARM_DISTANCE / vessel_speed;
    end

    % Process each turbine in order
    for i = 1:turbine_count
        turbine_idx = turbine_order(i);
        vessel_idx = vessel_assignment(i);

        % Get vessel properties
        vessel_type = vessels(vessel_idx).type; % 1=single-hull, 2=dual-hull
        vessel_stability = vessels(vessel_idx).stability;
        vessel_sea_efficiency = vessels(vessel_idx).sea_efficiency;
        vessel_max_assembly = vessels(vessel_idx).max_assembly_level;

        % Get sea condition for this turbine's location
        sea_idx = turbines(turbine_idx).sea_condition_index;
        wave_height = SEA_CONDITIONS.wave_height(sea_idx);
        wind_speed = SEA_CONDITIONS.wind_speed(sea_idx);
        current_speed = SEA_CONDITIONS.current_speed(sea_idx);

        % Calculate sea condition impact factor (higher means worse conditions)
        sea_impact = (wave_height/4) + (wind_speed/15) + (current_speed/2);
        sea_impact = min(max(sea_impact, 0), 1); % Normalize to 0-1

        % Apply vessel stability to reduce the impact
        effective_sea_impact = sea_impact * (1 - vessel_stability);

        % Get process times for this turbine
        process_times = turbines(turbine_idx).processes;
        num_processes = length(process_times);

        % 根据船舶类型确定组装级别
        % 获取船舶类型 (1=单体船, 2=双体船)
        vessel_type = vessels(vessel_idx).type;

        if vessel_type == 1
            % 单体船不使用组装
            assembly_level = 0;
        else
            % 双体船使用完全组装
            assembly_level = 2;
        end

        % 存储组装级别
        turbines(turbine_idx).assembly_level = assembly_level;

        % Handle berth assignment for loading
        [earliest_berth_time, berth_idx] = min(berth_avail_time);

        % Calculate loading start time (max of vessel and berth availability)
        loading_start = max(vessel_avail_time(vessel_idx), earliest_berth_time);
        loading_end = loading_start + vessels(vessel_idx).loading_time;

        % Update berth availability
        berth_avail_time(berth_idx) = loading_end;

        % Add loading operation to schedule
        schedule.turbine_id(end+1) = turbine_idx;
        schedule.vessel_id(end+1) = vessel_idx;
        schedule.start_time(end+1) = loading_start;
        schedule.end_time(end+1) = loading_end;
        schedule.process_id(end+1) = 0; % 0 for loading
        schedule.berth_id(end+1) = berth_idx;
        schedule.sea_condition(end+1) = 0; % No sea impact at berth
        schedule.assembly_level(end+1) = assembly_level;

        % 添加船上组装过程（仅对双体船）
        if assembly_level == 2
            % 获取完全组装时间
            ship_assembly_time = turbines(turbine_idx).ship_assembly_time.complete;

            ship_assembly_start = loading_end;
            ship_assembly_end = ship_assembly_start + ship_assembly_time;

            % 添加船上组装到调度
            schedule.turbine_id(end+1) = turbine_idx;
            schedule.vessel_id(end+1) = vessel_idx;
            schedule.start_time(end+1) = ship_assembly_start;
            schedule.end_time(end+1) = ship_assembly_end;
            schedule.process_id(end+1) = 5; % 5表示船上组装
            schedule.berth_id(end+1) = berth_idx; % 组装在泊位进行
            schedule.sea_condition(end+1) = 0; % 泊位没有海况影响
            schedule.assembly_level(end+1) = assembly_level;

            % 更新装载结束时间
            loading_end = ship_assembly_end;
        end

        % Travel to farm - affected by sea conditions
        travel_start = loading_end;
        % Adjust travel time based on sea conditions
        travel_time = port_to_farm_time(vessel_idx) * (1 + effective_sea_impact * 0.3);
        travel_end = travel_start + travel_time;

        % Add travel to schedule
        schedule.turbine_id(end+1) = turbine_idx;
        schedule.vessel_id(end+1) = vessel_idx;
        schedule.start_time(end+1) = travel_start;
        schedule.end_time(end+1) = travel_end;
        schedule.process_id(end+1) = -1; % -1 for travel
        schedule.berth_id(end+1) = 0;  % 0 for no berth
        schedule.sea_condition(end+1) = sea_impact;
        schedule.assembly_level(end+1) = assembly_level;

        % 处理安装任务
        current_time = travel_end;

        if assembly_level == 0 % 单体船 - 无组装
            % 根据海况调整工序时间
            time_increase = 1 + effective_sea_impact * 0.5; % 最多增加50%
            adjusted_process_times = process_times * time_increase;

            % 执行所有常规工序
            for p = 1:num_processes
                process_start = current_time;
                process_end = process_start + adjusted_process_times(p);

                % 添加工序到调度
                schedule.turbine_id(end+1) = turbine_idx;
                schedule.vessel_id(end+1) = vessel_idx;
                schedule.start_time(end+1) = process_start;
                schedule.end_time(end+1) = process_end;
                schedule.process_id(end+1) = p;
                schedule.berth_id(end+1) = 0;  % 0表示无泊位
                schedule.sea_condition(end+1) = sea_impact;
                schedule.assembly_level(end+1) = assembly_level;

                current_time = process_end;
            end
        else % 双体船 - 完全组装
            % 对于完全组装，我们用一个最终安装工序替代所有常规工序
            final_installation_time = FINAL_INSTALLATION_TIME * (0.9 + 0.2*rand());

            % 应用海况影响（但比常规工序影响小）
            final_installation_time = final_installation_time * (1 + effective_sea_impact * 0.2);

            process_start = current_time;
            process_end = process_start + final_installation_time;

            % 添加最终安装工序到调度
            schedule.turbine_id(end+1) = turbine_idx;
            schedule.vessel_id(end+1) = vessel_idx;
            schedule.start_time(end+1) = process_start;
            schedule.end_time(end+1) = process_end;
            schedule.process_id(end+1) = 6; % 6表示最终安装（完全组装）
            schedule.berth_id(end+1) = 0;  % 0表示无泊位
            schedule.sea_condition(end+1) = sea_impact;
            schedule.assembly_level(end+1) = assembly_level;

            current_time = process_end;
        end

        % Travel back to port - affected by sea conditions
        travel_back_start = current_time;
        travel_back_time = port_to_farm_time(vessel_idx) * (1 + effective_sea_impact * 0.3);
        travel_back_end = travel_back_start + travel_back_time;

        % Add travel back to schedule
        schedule.turbine_id(end+1) = turbine_idx;
        schedule.vessel_id(end+1) = vessel_idx;
        schedule.start_time(end+1) = travel_back_start;
        schedule.end_time(end+1) = travel_back_end;
        schedule.process_id(end+1) = -2; % -2 for travel back
        schedule.berth_id(end+1) = 0;  % 0 for no berth
        schedule.sea_condition(end+1) = sea_impact;
        schedule.assembly_level(end+1) = assembly_level;

        % Update vessel availability
        vessel_avail_time(vessel_idx) = travel_back_end;
    end
end

function makespan = calculateMakespan(schedule)
    % Calculate makespan (maximum completion time)
    makespan = max(schedule.end_time);
end

function diversity = calculateDiversity(fitness)
    % Calculate population diversity based on fitness variance
    mean_fitness = mean(fitness);
    normalized_std = std(fitness) / mean_fitness;
    diversity = normalized_std;
end

function child = crossover(parent1, parent2, turbine_count)
    % Order Crossover (OX) for turbine_order
    % Select two crossover points
    points = sort(randperm(turbine_count, 2));
    p1 = points(1);
    p2 = points(2);

    % Initialize child
    child = struct('turbine_order', zeros(1, turbine_count), ...
                  'vessel_assignment', zeros(1, turbine_count));

    % Copy segment from parent1
    child.turbine_order(p1:p2) = parent1.turbine_order(p1:p2);

    % Fill remaining positions with elements from parent2
    remaining = setdiff(parent2.turbine_order, parent1.turbine_order(p1:p2));

    % Fill positions before p1
    child.turbine_order(1:p1-1) = remaining(1:(p1-1));

    % Fill positions after p2
    child.turbine_order(p2+1:end) = remaining(p1:end);

    % Uniform crossover for vessel assignment
    mask = rand(1, turbine_count) > 0.5;
    child.vessel_assignment = parent1.vessel_assignment;
    child.vessel_assignment(mask) = parent2.vessel_assignment(mask);
end

function child = mutate(child, vessel_count, turbine_count)
    % Swap mutation for turbine order
    idx = randperm(turbine_count, 2);
    temp = child.turbine_order(idx(1));
    child.turbine_order(idx(1)) = child.turbine_order(idx(2));
    child.turbine_order(idx(2)) = temp;

    % Random reassignment for vessel assignment
    idx = randi(turbine_count);
    child.vessel_assignment(idx) = randi(vessel_count);
end

function right_shift_schedule = rightShiftRescheduling(schedule, disruption)
    % 右移重调度函数 - 修正版
    % 输入：原调度计划，中断信息
    % 输出：调整后的调度

    % 复制原调度
    right_shift_schedule = schedule;

    % 提取中断信息
    affected_vessel = disruption.affected_vessel;
    t_d = disruption.time; % 中断发生时间
    t_r = disruption.repair_time; % 维修所需时间

    % 1. 识别受影响的任务
    % 找出故障船舶在中断时正在执行的任务
    ongoing_idx = find(schedule.vessel_id == affected_vessel & ...
                      schedule.start_time < t_d & ...
                      schedule.end_time > t_d);

    % 找出故障船舶后续的所有任务，按开始时间排序
    future_idx = find(schedule.vessel_id == affected_vessel & ...
                     schedule.start_time >= t_d);
    
    % 按开始时间排序，确保任务处理顺序正确
    if ~isempty(future_idx)
        [~, sort_order] = sort(right_shift_schedule.start_time(future_idx));
        future_idx = future_idx(sort_order);
    end

    % 2. 处理正在进行的任务：将其结束时间设为t_d，并计算剩余时间
    remaining_time = 0;
    interrupted_task_info = [];
    
    for i = 1:length(ongoing_idx)
        task_idx = ongoing_idx(i);
        % 记录剩余处理时间
        remaining_time = right_shift_schedule.end_time(task_idx) - t_d;
        % 保存任务信息用于后续恢复
        interrupted_task_info = struct(...
            'turbine_id', right_shift_schedule.turbine_id(task_idx), ...
            'process_id', right_shift_schedule.process_id(task_idx), ...
            'berth_id', right_shift_schedule.berth_id(task_idx), ...
            'sea_condition', right_shift_schedule.sea_condition(task_idx), ...
            'assembly_level', right_shift_schedule.assembly_level(task_idx), ...
            'remaining_time', remaining_time);
        % 更新当前任务的结束时间为中断时间
        right_shift_schedule.end_time(task_idx) = t_d;
    end

    % 3. 插入维修任务
    repair_task_idx = length(right_shift_schedule.turbine_id) + 1;
    right_shift_schedule.turbine_id(repair_task_idx) = 0; % 0表示维修任务
    right_shift_schedule.vessel_id(repair_task_idx) = affected_vessel;
    right_shift_schedule.start_time(repair_task_idx) = t_d;
    right_shift_schedule.end_time(repair_task_idx) = t_d + t_r;
    right_shift_schedule.process_id(repair_task_idx) = -3; % 特殊标识
    right_shift_schedule.berth_id(repair_task_idx) = 0;
    right_shift_schedule.sea_condition(repair_task_idx) = 0; % 维修不受海况影响
    right_shift_schedule.assembly_level(repair_task_idx) = 0; % 维修没有组装级别

    % 4. 计算新的任务开始时间，确保连续性
    current_vessel_time = t_d + t_r; % 维修后船舶可用时间

    % 4.1 继续未完成的任务（如果有）
    if ~isempty(interrupted_task_info) && interrupted_task_info.remaining_time > 0
        cont_task_idx = length(right_shift_schedule.turbine_id) + 1;
        right_shift_schedule.turbine_id(cont_task_idx) = interrupted_task_info.turbine_id;
        right_shift_schedule.vessel_id(cont_task_idx) = affected_vessel;
        right_shift_schedule.start_time(cont_task_idx) = current_vessel_time;
        right_shift_schedule.end_time(cont_task_idx) = current_vessel_time + interrupted_task_info.remaining_time;
        right_shift_schedule.process_id(cont_task_idx) = interrupted_task_info.process_id;
        right_shift_schedule.berth_id(cont_task_idx) = interrupted_task_info.berth_id;
        right_shift_schedule.sea_condition(cont_task_idx) = interrupted_task_info.sea_condition;
        right_shift_schedule.assembly_level(cont_task_idx) = interrupted_task_info.assembly_level;
        
        % 更新船舶可用时间
        current_vessel_time = current_vessel_time + interrupted_task_info.remaining_time;
    end

    % 5. 对故障船舶的所有后续任务进行连续调整 - 完全修正版
    % 确保所有后续任务连续，无间隙
    if ~isempty(future_idx)
        % 按原始开始时间排序所有后续任务，保持原有顺序
        [~, sort_order] = sort(right_shift_schedule.start_time(future_idx));
        future_idx_sorted = future_idx(sort_order);

        % 连续调整所有后续任务，确保无间隙
        for i = 1:length(future_idx_sorted)
            task_idx = future_idx_sorted(i);

            % 计算任务持续时间
            duration = right_shift_schedule.end_time(task_idx) - right_shift_schedule.start_time(task_idx);

            % 设置新的开始时间为当前船舶可用时间
            right_shift_schedule.start_time(task_idx) = current_vessel_time;
            right_shift_schedule.end_time(task_idx) = current_vessel_time + duration;

            % 更新船舶可用时间为当前任务结束时间
            current_vessel_time = current_vessel_time + duration;
        end
    end

    % 6. 处理泊位冲突：调整使用同一泊位的其他船舶任务
    % 获取故障船舶原本使用的泊位
    affected_berths = [];
    if ~isempty(ongoing_idx)
        affected_berths = unique(right_shift_schedule.berth_id(ongoing_idx));
    end
    if ~isempty(future_idx)
        affected_berths = unique([affected_berths, right_shift_schedule.berth_id(future_idx)]);
    end
    affected_berths = affected_berths(affected_berths > 0); % 排除0

    for b = 1:length(affected_berths)
        berth = affected_berths(b);
        % 找到在维修期间及之后使用该泊位的其他船舶任务
        conflict_tasks = find(right_shift_schedule.berth_id == berth & ...
                             right_shift_schedule.vessel_id ~= affected_vessel & ...
                             right_shift_schedule.start_time >= t_d);

        if ~isempty(conflict_tasks)
            % 按原开始时间排序
            [~, order] = sort(right_shift_schedule.start_time(conflict_tasks));
            conflict_tasks = conflict_tasks(order);

            % 计算泊位最早可用时间（考虑故障船舶在该泊位的新任务）
            berth_available_time = t_d + t_r;
            
            % 检查故障船舶是否有在维修后使用该泊位的任务
            affected_vessel_berth_tasks = find(right_shift_schedule.berth_id == berth & ...
                                              right_shift_schedule.vessel_id == affected_vessel & ...
                                              right_shift_schedule.start_time >= t_d + t_r);
            if ~isempty(affected_vessel_berth_tasks)
                % 找到最晚结束的任务时间
                berth_available_time = max(right_shift_schedule.end_time(affected_vessel_berth_tasks));
            end

            % 逐个调整冲突任务，保持时间连续性
            prev_end = berth_available_time;
            for j = 1:length(conflict_tasks)
                task_idx = conflict_tasks(j);
                duration = right_shift_schedule.end_time(task_idx) - right_shift_schedule.start_time(task_idx);
                
                % 确保任务不早于原计划开始，也不早于前一个任务结束
                new_start = max(prev_end, right_shift_schedule.start_time(task_idx));
                right_shift_schedule.start_time(task_idx) = new_start;
                right_shift_schedule.end_time(task_idx) = new_start + duration;
                
                prev_end = new_start + duration;
            end
        end
    end

    % 7. 验证调度结果
    validateSchedule(right_shift_schedule, disruption);
end

function validateSchedule(schedule, disruption)
    % 验证调度结果是否满足约束条件
    t_d = disruption.time;
    t_r = disruption.repair_time;
    affected_vessel = disruption.affected_vessel;

    % 检查维修任务
    repair_tasks = find(schedule.process_id == -3 & schedule.vessel_id == affected_vessel);
    if isempty(repair_tasks)
        warning('未找到维修任务');
    else
        repair_start = schedule.start_time(repair_tasks(1));
        repair_end = schedule.end_time(repair_tasks(1));
        if abs(repair_start - t_d) > 1e-6 || abs(repair_end - (t_d + t_r)) > 1e-6
            warning('维修时间不匹配: 应为 [%g, %g], 实际为 [%g, %g]', ...
                t_d, t_d + t_r, repair_start, repair_end);
        end
    end

    % 检查受影响任务
    vessel_tasks = find(schedule.vessel_id == affected_vessel & schedule.process_id ~= -3);
    for i = 1:length(vessel_tasks)
        idx = vessel_tasks(i);
        if schedule.start_time(idx) >= t_d && schedule.start_time(idx) < t_d + t_r
            warning('任务 %d 未正确右移: 开始于 %g, 应该至少从 %g 开始', ...
                idx, schedule.start_time(idx), t_d + t_r);
        end
    end

    % 检查资源约束是否满足
    vessels = unique(schedule.vessel_id);
    for v = 1:length(vessels)
        v_id = vessels(v);
        v_tasks = find(schedule.vessel_id == v_id);
        if length(v_tasks) <= 1
            continue;
        end

        % 按开始时间排序
        [~, sorted_idx] = sort(schedule.start_time(v_tasks));
        v_tasks_sorted = v_tasks(sorted_idx);

        % 检查是否有重叠
        for i = 1:length(v_tasks_sorted)-1
            task1 = v_tasks_sorted(i);
            task2 = v_tasks_sorted(i+1);
            if schedule.end_time(task1) > schedule.start_time(task2)
                warning('船舶 %d 的任务 %d 和 %d 时间重叠', ...
                    v_id, task1, task2);
            end
        end
    end
end

% 完全重调度函数已移除


%% Schedule Merge Function (新增)
function merged = mergeSchedules(schedule1, schedule2)
    % 合并两个调度方案的数据结构
    merged = struct();
    fields = fieldnames(schedule1);
    for f = 1:numel(fields)
        merged.(fields{f}) = [schedule1.(fields{f}), schedule2.(fields{f})];
    end
end
function remaining_schedule = generateRemainingSchedule(turbines, vessels, berths, vessel_avail_time, start_time)
    % 使用修改后的hybridGAPSO为剩余任务生成调度计划

    % 获取问题规模
    turbine_count = length(turbines);
    vessel_count = length(vessels);

    % 获取全局参数
    global POP_SIZE MAX_GEN PSO_ITERATIONS CROSSOVER_RATE MUTATION_RATE_GA...
           MUTATION_RATE_PSO INERTIA_WEIGHT C1 C2 DIVERSITY_THRESHOLD...
           FINAL_INSTALLATION_TIME;

    % 定义较小的种群规模和迭代次数，加快重调度速度
    local_pop_size = max(20, min(POP_SIZE, 30));
    local_max_gen = max(50, min(MAX_GEN, 80));

    % 初始化种群
    population = cell(1, local_pop_size);
    fitness = zeros(1, local_pop_size);

    % 生成初始种群
    for i = 1:local_pop_size
        % 涡轮机安装顺序随机排列
        turbine_order = randperm(turbine_count);
        % 船舶分配随机选择
        vessel_assignment = randi(vessel_count, 1, turbine_count);
        population{i} = struct('turbine_order', turbine_order, ...
                              'vessel_assignment', vessel_assignment);
    end

    % 初始化PSO参数
    pbest = population;
    pbest_fitness = Inf(1, local_pop_size);
    gbest = [];
    gbest_fitness = Inf;
    velocity = cell(1, local_pop_size);

    % 初始化速度
    for i = 1:local_pop_size
        velocity{i} = struct('turbine_order', zeros(1, turbine_count), ...
                            'vessel_assignment', zeros(1, turbine_count));
    end

    % 主循环
    for gen = 1:local_max_gen
        % 评估种群
        for i = 1:local_pop_size
            % 将染色体解码为调度计划，考虑船舶初始可用时间
            schedule_i = decodeChromosomeWithStartTime(population{i}, ...
                turbines, vessels, berths, vessel_avail_time, start_time);
            % 计算工期
            fitness(i) = calculateMakespan(schedule_i);

            % 更新个体最优
            if fitness(i) < pbest_fitness(i)
                pbest{i} = population{i};
                pbest_fitness(i) = fitness(i);
            end
        end

        % 更新全局最优
        [min_fitness, min_idx] = min(fitness);
        if min_fitness < gbest_fitness
            gbest = population{min_idx};
            gbest_fitness = min_fitness;
        end

        % 监控收敛性
        if mod(gen, 10) == 0
            fprintf('重调度Generation %d: Best makespan = %.2f hours\n', gen, gbest_fitness);
        end

        % 计算种群多样性
        diversity = calculateDiversity(fitness);

        % 如果多样性低于阈值，应用PSO
        if diversity < DIVERSITY_THRESHOLD
            fprintf('重调度过程中检测到低多样性，在第%d代应用PSO...\n', gen);

            % 应用PSO进行局部搜索
            for pso_iter = 1:PSO_ITERATIONS
                for i = 1:local_pop_size
                    % 更新速度
                    velocity{i}.turbine_order = INERTIA_WEIGHT * velocity{i}.turbine_order + ...
                        C1 * rand() * (pbest{i}.turbine_order - population{i}.turbine_order) + ...
                        C2 * rand() * (gbest.turbine_order - population{i}.turbine_order);

                    velocity{i}.vessel_assignment = INERTIA_WEIGHT * velocity{i}.vessel_assignment + ...
                        C1 * rand() * (pbest{i}.vessel_assignment - population{i}.vessel_assignment) + ...
                        C2 * rand() * (gbest.vessel_assignment - population{i}.vessel_assignment);

                    % 更新位置
                    % 对于涡轮机顺序（排列），使用特殊方法
                    new_turbine_order = population{i}.turbine_order + round(velocity{i}.turbine_order);
                    % 确保它仍然是有效的排列
                    [~, new_turbine_order] = sort(new_turbine_order);

                    % 对于船舶分配
                    new_vessel_assignment = population{i}.vessel_assignment + round(velocity{i}.vessel_assignment);
                    % 确保有效（在1和vessel_count之间）
                    new_vessel_assignment = max(1, min(vessel_count, new_vessel_assignment));

                    % 以较高概率应用变异
                    if rand() < MUTATION_RATE_PSO
                        % 在涡轮机顺序中交换两个位置
                        idx = randperm(turbine_count, 2);
                        temp = new_turbine_order(idx(1));
                        new_turbine_order(idx(1)) = new_turbine_order(idx(2));
                        new_turbine_order(idx(2)) = temp;
                    end

                    % 更新种群
                    population{i}.turbine_order = new_turbine_order;
                    population{i}.vessel_assignment = new_vessel_assignment;

                    % 评估新解
                    schedule_i = decodeChromosomeWithStartTime(population{i}, ...
                        turbines, vessels, berths, vessel_avail_time, start_time);
                    fitness(i) = calculateMakespan(schedule_i);

                    % 更新个体最优
                    if fitness(i) < pbest_fitness(i)
                        pbest{i} = population{i};
                        pbest_fitness(i) = fitness(i);
                    end
                end

                % 更新全局最优
                [min_fitness, min_idx] = min(fitness);
                if min_fitness < gbest_fitness
                    gbest = population{min_idx};
                    gbest_fitness = min_fitness;
                end
            end
        else
            % GA操作（选择、交叉、变异）
            new_population = cell(1, local_pop_size);

            % 精英选择 - 保留最佳解
            [~, elite_idx] = min(fitness);
            new_population{1} = population{elite_idx};

            % 对剩余个体进行选择、交叉和变异
            for i = 2:local_pop_size
                % 锦标赛选择
                tour_idx = randperm(local_pop_size, 2);
                if fitness(tour_idx(1)) < fitness(tour_idx(2))
                    parent1 = population{tour_idx(1)};
                else
                    parent1 = population{tour_idx(2)};
                end

                tour_idx = randperm(local_pop_size, 2);
                if fitness(tour_idx(1)) < fitness(tour_idx(2))
                    parent2 = population{tour_idx(1)};
                else
                    parent2 = population{tour_idx(2)};
                end

                % 交叉
                if rand() < CROSSOVER_RATE
                    child = crossover(parent1, parent2, turbine_count);
                else
                    child = parent1;
                end

                % 变异
                if rand() < MUTATION_RATE_GA
                    child = mutate(child, vessel_count, turbine_count);
                end

                new_population{i} = child;
            end

            population = new_population;
        end

        % 检查终止条件
        if gen >= local_max_gen
            break;
        end
    end

    % 将最佳解转换为调度计划
    remaining_schedule = decodeChromosomeWithStartTime(gbest, ...
        turbines, vessels, berths, vessel_avail_time, start_time);
end

function schedule = decodeChromosomeWithStartTime(chromosome, turbines, vessels, berths, vessel_avail_time, start_time)
    % 与decodeChromosome类似，但考虑初始可用时间
    global PORT_TO_FARM_DISTANCE SEA_CONDITIONS FINAL_INSTALLATION_TIME;

    turbine_order = chromosome.turbine_order;
    vessel_assignment = chromosome.vessel_assignment;

    % 初始化调度数据结构
    turbine_count = length(turbines);
    vessel_count = length(vessels);
    berth_count = length(berths);

    % 初始化泊位可用时间
    berth_avail_time = ones(1, berth_count) * start_time;

    % 初始化调度
    schedule = struct('turbine_id', [], 'vessel_id', [], ...
                     'start_time', [], 'end_time', [], ...
                     'process_id', [], 'berth_id', [], ...
                     'sea_condition', [], 'assembly_level', []);

    % 港口到风场的航行时间
    port_to_farm_time = zeros(1, vessel_count);
    for v = 1:vessel_count
        vessel_speed = vessels(v).speed;
        port_to_farm_time(v) = PORT_TO_FARM_DISTANCE / vessel_speed;
    end

    % 处理每个涡轮机
    for i = 1:turbine_count
        turbine_idx = turbines(i).id;  % 使用正确的涡轮机ID
        vessel_idx = vessel_assignment(i);

        % 获取船舶属性
        vessel_type = vessels(vessel_idx).type; % 1=单体船，2=双体船
        vessel_stability = vessels(vessel_idx).stability;
        vessel_sea_efficiency = vessels(vessel_idx).sea_efficiency;
        vessel_max_assembly = vessels(vessel_idx).max_assembly_level;

        % 获取此涡轮机位置的海况
        sea_idx = turbines(i).sea_condition_index;
        wave_height = SEA_CONDITIONS.wave_height(sea_idx);
        wind_speed = SEA_CONDITIONS.wind_speed(sea_idx);
        current_speed = SEA_CONDITIONS.current_speed(sea_idx);

        % 计算海况影响因子（越高表示条件越差）
        sea_impact = (wave_height/4) + (wind_speed/15) + (current_speed/2);
        sea_impact = min(max(sea_impact, 0), 1); % 归一化到0-1

        % 应用船舶稳定性来减少影响
        effective_sea_impact = sea_impact * (1 - vessel_stability);

        % 获取此涡轮机的处理时间
        process_times = turbines(i).processes;  % 使用turbines(i)而不是turbine_idx
        num_processes = length(process_times);

        % 根据船舶类型确定组装级别
        if vessel_type == 1
            % 单体船不使用组装
            assembly_level = 0;
        else
            % 双体船使用完全组装
            assembly_level = 2;
        end

        % 存储组装级别
        turbines(i).assembly_level = assembly_level;

        % 处理泊位分配
        [earliest_berth_time, berth_idx] = min(berth_avail_time);

        % 计算装载开始时间
        loading_start = max(vessel_avail_time(vessel_idx), earliest_berth_time);
        loading_end = loading_start + vessels(vessel_idx).loading_time;

        % 更新泊位可用性
        berth_avail_time(berth_idx) = loading_end;        % 添加装载操作到调度
        schedule.turbine_id(end+1) = turbine_idx;
        schedule.vessel_id(end+1) = vessel_idx;
        schedule.start_time(end+1) = loading_start;
        schedule.end_time(end+1) = loading_end;
        schedule.process_id(end+1) = 0; % 0表示装载
        schedule.berth_id(end+1) = berth_idx;
        schedule.sea_condition(end+1) = 0; % 泊位没有海况影响
        schedule.assembly_level(end+1) = assembly_level;        % 添加船上组装过程（仅对双体船）
        if assembly_level == 2
            % 获取完全组装时间
            ship_assembly_time = turbines(i).ship_assembly_time.complete;

            ship_assembly_start = loading_end;
            ship_assembly_end = ship_assembly_start + ship_assembly_time;

            % 添加船上组装到调度
            schedule.turbine_id(end+1) = turbine_idx;
            schedule.vessel_id(end+1) = vessel_idx;
            schedule.start_time(end+1) = ship_assembly_start;
            schedule.end_time(end+1) = ship_assembly_end;
            schedule.process_id(end+1) = 5; % 5表示船上组装
            schedule.berth_id(end+1) = berth_idx;  % 在泊位进行
            schedule.sea_condition(end+1) = 0; % 泊位没有海况影响
            schedule.assembly_level(end+1) = assembly_level;

            % 更新泊位时间
            berth_avail_time(berth_idx) = ship_assembly_end;

            % 更新旅行开始时间
            travel_start = ship_assembly_end;
        else
            % 对于单体船，不需要船上组装
            travel_start = loading_end;
        end

        % 前往风场
        travel_end = travel_start + port_to_farm_time(vessel_idx);        % 添加航行到调度
        schedule.turbine_id(end+1) = turbine_idx;
        schedule.vessel_id(end+1) = vessel_idx;
        schedule.start_time(end+1) = travel_start;
        schedule.end_time(end+1) = travel_end;
        schedule.process_id(end+1) = -1; % -1表示前往风场
        schedule.berth_id(end+1) = 0;  % 0表示无泊位
        schedule.sea_condition(end+1) = effective_sea_impact; % 航行受海况影响
        schedule.assembly_level(end+1) = assembly_level;        % 处理安装任务
        current_time = travel_end;

        % 根据组装级别和船舶类型处理安装任务
        if assembly_level == 2  % 双体船完全组装
            % 对于完全组装，只需一个最终安装步骤
            process_start = current_time;

            % 应用海况对安装时间的影响
            adjusted_time = FINAL_INSTALLATION_TIME * (1 + effective_sea_impact);
            process_end = process_start + adjusted_time;

            % 添加最终安装到调度
            schedule.turbine_id(end+1) = turbine_idx;
            schedule.vessel_id(end+1) = vessel_idx;
            schedule.start_time(end+1) = process_start;
            schedule.end_time(end+1) = process_end;
            schedule.process_id(end+1) = 6;  % 6表示最终安装(完全组装)
            schedule.berth_id(end+1) = 0;    % 0表示无泊位
            schedule.sea_condition(end+1) = effective_sea_impact;
            schedule.assembly_level(end+1) = assembly_level;

            current_time = process_end;
        else  % 单体船常规安装
            for p = 1:num_processes
                process_start = current_time;

                % 应用海况影响工序时间
                adjusted_time = process_times(p) * (1 + effective_sea_impact);
                process_end = process_start + adjusted_time;

                % 添加流程到调度
                schedule.turbine_id(end+1) = turbine_idx;
                schedule.vessel_id(end+1) = vessel_idx;
                schedule.start_time(end+1) = process_start;
                schedule.end_time(end+1) = process_end;
                schedule.process_id(end+1) = p;
                schedule.berth_id(end+1) = 0;  % 0表示无泊位
                schedule.sea_condition(end+1) = effective_sea_impact;
                schedule.assembly_level(end+1) = assembly_level;

                current_time = process_end;
            end
        end

        % 返回港口
        travel_back_start = current_time;
        travel_back_end = travel_back_start + port_to_farm_time(vessel_idx);        % 添加返回航行到调度
        schedule.turbine_id(end+1) = turbine_idx;
        schedule.vessel_id(end+1) = vessel_idx;
        schedule.start_time(end+1) = travel_back_start;
        schedule.end_time(end+1) = travel_back_end;
        schedule.process_id(end+1) = -2; % -2表示返回港口
        schedule.berth_id(end+1) = 0;  % 0表示无泊位
        schedule.sea_condition(end+1) = effective_sea_impact; % 航行受海况影响
        schedule.assembly_level(end+1) = assembly_level;
        schedule.berth_id(end+1) = 0;  % 0表示无泊位

        % 更新船舶可用性
        vessel_avail_time(vessel_idx) = travel_back_end;
    end
end

function plotGanttChart(schedule, title_text, turbines, vessels, disruption)
    % 创建专业外观的甘特图 - 修正版
    % 包含正确的维修任务显示和双船模型支持

    figure('Position', [100, 100, 1200, 800], 'Color', 'white');

    % 使用柔和的颜色调色板，与shuangchuan代码保持一致
    process_colors = [
        0.9290, 0.6940, 0.3250;  % 装载 (process 0) - 柔和橙色
        0.4660, 0.7740, 0.8880;  % 前往风场 (process -1) - 柔和蓝色
        0.6350, 0.5040, 0.7410;  % 基础安装 (process 1) - 柔和紫色
        0.4660, 0.7410, 0.3880;  % 塔筒安装 (process 2) - 柔和绿色
        0.8500, 0.3250, 0.3980;  % 机舱安装 (process 3) - 柔和红色
        0.9290, 0.6940, 0.1250;  % 叶片安装 (process 4) - 柔和黄色
        0.4660, 0.7740, 0.8880;  % 返回港口 (process -2) - 柔和蓝色
        1.0000, 0.3000, 0.3000;  % 维修 (process -3) - 醒目但不太亮的红色
        0.3010, 0.7450, 0.5330;  % 船上组装 (process 5) - 青色
        0.4940, 0.1840, 0.5560;  % 最终安装 (process 6) - 紫色
    ];

    % 工序名称（用于图例）
    process_names = {'装载', '前往风场', '基础安装', '塔筒安装', '机舱安装', '叶片安装', '返回港口', '维修', '船上组装', '最终安装'};

    % 创建图例的虚拟对象
    dummy_handles = zeros(1, length(process_names));
    for i = 1:length(process_names)
        dummy_handles(i) = plot(NaN, NaN, 'LineWidth', 8, 'Color', process_colors(i,:));
        hold on;
    end

    % 绘制船舶时间线
    hold on;

    % 识别单体船和双体船索引
    single_hull_indices = [];
    dual_hull_indices = [];
    if isfield(vessels, 'type')
        single_hull_indices = find([vessels.type] == 1);
        dual_hull_indices = find([vessels.type] == 2);
    end

    % 如果有故障，添加故障标记线
    if ~isempty(disruption)
        % 绘制故障发生的垂直线
        line([disruption.time, disruption.time], [0, length(vessels)+5], ...
            'Color', 'r', 'LineStyle', '--', 'LineWidth', 1.5);

        % 添加故障标记文本
        text(disruption.time, length(vessels)+2.5, '故障发生', ...
            'HorizontalAlignment', 'center', 'Color', 'r', ...
            'FontWeight', 'bold', 'VerticalAlignment', 'bottom');

        % 添加结束修复的垂直线
        repair_end = disruption.time + disruption.repair_time;
        line([repair_end, repair_end], [0, length(vessels)+3], ...
            'Color', 'r', 'LineStyle', ':', 'LineWidth', 1.0);

        % 添加修复结束标记文本
        text(repair_end, length(vessels)+2.5, '修复结束', ...
            'HorizontalAlignment', 'center', 'Color', 'r', ...
            'FontWeight', 'bold', 'VerticalAlignment', 'bottom');
    end

    % 按船舶组织任务
    for v = 1:length(vessels)
        vessel_tasks = find(schedule.vessel_id == v);
        y_pos = v;

        for i = 1:length(vessel_tasks)
            task_idx = vessel_tasks(i);
            start_time = schedule.start_time(task_idx);
            end_time = schedule.end_time(task_idx);
            duration = end_time - start_time;            % 确定过程类型
            process_id = schedule.process_id(task_idx);

            % 将process_id映射到颜色索引 - 确保包含维修任务和组装任务
            if process_id == 0  % 装载
                color_idx = 1;
                process_label = 'L';
            elseif process_id == -1  % 前往风场
                color_idx = 2;
                process_label = 'TF';
            elseif process_id == -2  % 返回港口
                color_idx = 7;
                process_label = 'TB';
            elseif process_id == -3  % 维修
                color_idx = 8;
                process_label = 'R';
            elseif process_id == 5  % 船上组装
                color_idx = 9;
                process_label = 'SA';
            elseif process_id == 6  % 最终组装
                color_idx = 10;
                process_label = 'FA';
            else  % 常规过程
                color_idx = min(process_id + 2, 7);  % 确保不超出范围
                process_label = ['P', num2str(process_id)];
            end

            % Get assembly level if available
            assembly_level = 0; % Default: no assembly
            if isfield(schedule, 'assembly_level') && length(schedule.assembly_level) >= task_idx
                assembly_level = schedule.assembly_level(task_idx);
            end

            % 根据组装级别绘制任务条
            if assembly_level == 0 % 无组装 - 普通边框
                rectangle('Position', [start_time, y_pos-0.4, duration, 0.8], ...
                    'FaceColor', process_colors(color_idx,:), ...
                    'EdgeColor', 'k', 'LineWidth', 0.5);
            elseif assembly_level == 1 % 部分组装 - 虚线边框
                rectangle('Position', [start_time, y_pos-0.4, duration, 0.8], ...
                    'FaceColor', process_colors(color_idx,:), ...
                    'EdgeColor', 'k', 'LineWidth', 0.5, 'LineStyle', ':');
            else % 完全组装 - 粗边框
                rectangle('Position', [start_time, y_pos-0.4, duration, 0.8], ...
                    'FaceColor', process_colors(color_idx,:), ...
                    'EdgeColor', 'k', 'LineWidth', 1.5);
            end

            % Add sea condition indicator if available
            if isfield(schedule, 'sea_condition') && length(schedule.sea_condition) >= task_idx
                sea_impact = schedule.sea_condition(task_idx);
                if sea_impact > 0 % Only show for tasks with sea impact
                    % Determine sea condition color (green for good, yellow for medium, red for bad)
                    if sea_impact < 0.33
                        sea_color = [0, 0.7, 0]; % Green
                    elseif sea_impact < 0.66
                        sea_color = [0.9, 0.6, 0]; % Yellow/Orange
                    else
                        sea_color = [0.8, 0, 0]; % Red
                    end

                    % Draw a small line at the top of the task bar to indicate sea condition
                    line([start_time, start_time + duration], [y_pos+0.4, y_pos+0.4], ...
                        'Color', sea_color, 'LineWidth', 2);
                end
            end

            % 使用统一的较小字体大小
            font_size = 7;

            % 创建标签
            if process_id == -3  % 维修 - 特殊标签
                label_text = '维修';
            else
                if schedule.turbine_id(task_idx) == 0
                    label_text = process_label;
                else
                    label_text = sprintf('T%d-%s', schedule.turbine_id(task_idx), process_label);
                end
            end

            % 对于非常短的持续时间，使用文本旋转或外部标签
            if duration < 5
                % 使用带线的外部标签
                text(start_time + duration/2, y_pos + 0.5, label_text, ...
                    'HorizontalAlignment', 'left', ...
                    'VerticalAlignment', 'bottom', ...
                    'FontSize', font_size, ...
                    'Rotation', 45);
                % 绘制连接到块的小线
                line([start_time + duration/2, start_time + duration/2], [y_pos, y_pos + 0.4], 'Color', 'k', 'LineStyle', ':');
            else
                % 常规内部标签
                text(start_time + duration/2, y_pos, label_text, ...
                    'HorizontalAlignment', 'center', ...
                    'VerticalAlignment', 'middle', ...
                    'FontSize', font_size);
            end
        end        % 在y轴添加船舶标签
        % 检查是否是单体船还是双体船
        vessel_type_text = '';
        if isfield(vessels, 'type')
            if vessels(v).type == 1
                vessel_type_text = '(单体船)';
            else
                vessel_type_text = '(双体船)';
            end
        end

        % 如果这是受影响的船舶，添加标记
        if ~isempty(disruption) && v == disruption.affected_vessel
            text(-20, y_pos, sprintf('船舶 %d %s (故障)', v, vessel_type_text), ...
                'HorizontalAlignment', 'right', 'FontWeight', 'bold', 'Color', 'r');
        else
            text(-20, y_pos, sprintf('船舶 %d %s', v, vessel_type_text), ...
                'HorizontalAlignment', 'right', 'FontWeight', 'bold');
        end
    end

    % 添加泊位时间线
    berth_count = max(max(schedule.berth_id), 0);
    if berth_count > 0
        y_start = length(vessels) + 1;
        for b = 1:berth_count
            berth_tasks = find(schedule.berth_id == b);
            y_pos = y_start + b;

            for i = 1:length(berth_tasks)
                task_idx = berth_tasks(i);
                start_time = schedule.start_time(task_idx);
                end_time = schedule.end_time(task_idx);
                duration = end_time - start_time;

                % 绘制任务条（装载始终是process_id 0）
                rectangle('Position', [start_time, y_pos-0.4, duration, 0.8], ...
                    'FaceColor', process_colors(1,:), ...
                    'EdgeColor', 'k', 'LineWidth', 0.5);

                % 添加适当比例的文本标签
                if duration > 30
                    font_size = 8;
                elseif duration > 15
                    font_size = 7;
                else
                    font_size = 6;
                end

                % 创建标签
                label_text = sprintf('T%d-L', schedule.turbine_id(task_idx));

                if duration < 5
                    % 小持续时间的外部标签
                    text(start_time + duration/2, y_pos + 0.5, label_text, ...
                        'HorizontalAlignment', 'left', ...
                        'VerticalAlignment', 'bottom', ...
                        'FontSize', font_size, ...
                        'Rotation', 45);
                    line([start_time + duration/2, start_time + duration/2], [y_pos, y_pos + 0.4], 'Color', 'k', 'LineStyle', ':');
                else
                    % 常规内部标签
                    text(start_time + duration/2, y_pos, label_text, ...
                        'HorizontalAlignment', 'center', ...
                        'VerticalAlignment', 'middle', ...
                        'FontSize', font_size);
                end
            end

            % 在y轴添加泊位标签
            text(-20, y_pos, sprintf('泊位 %d', b), ...
                'HorizontalAlignment', 'right', 'FontWeight', 'bold');
        end
    end

    % Create dummy objects for vessel type legend
    vessel_type_handles = zeros(1, 2);
    vessel_type_handles(1) = plot(NaN, NaN, 'LineWidth', 2, 'Color', 'k', 'LineStyle', '-');
    vessel_type_handles(2) = plot(NaN, NaN, 'LineWidth', 2, 'Color', 'k', 'LineStyle', '--');
    vessel_type_names = {'单体船', '双体船'};

    % Create dummy objects for sea condition legend
    sea_condition_handles = zeros(1, 3);
    sea_condition_handles(1) = plot(NaN, NaN, 'LineWidth', 2, 'Color', [0, 0.7, 0], 'LineStyle', '-');
    sea_condition_handles(2) = plot(NaN, NaN, 'LineWidth', 2, 'Color', [0.9, 0.6, 0], 'LineStyle', '-');
    sea_condition_handles(3) = plot(NaN, NaN, 'LineWidth', 2, 'Color', [0.8, 0, 0], 'LineStyle', '-');
    sea_condition_names = {'良好海况', '中等海况', '恶劣海况'};

    % 创建组装级别图例的虚拟对象
    assembly_level_handles = zeros(1, 3);
    assembly_level_handles(1) = plot(NaN, NaN, 'LineWidth', 2, 'Color', 'k', 'LineStyle', '-');
    assembly_level_handles(2) = plot(NaN, NaN, 'LineWidth', 2, 'Color', 'k', 'LineStyle', ':');
    assembly_level_handles(3) = plot(NaN, NaN, 'LineWidth', 3, 'Color', 'k', 'LineStyle', '-');
    assembly_level_names = {'无组装', '部分组装', '完全组装'};

    % 添加图例，包括工序、船舶类型、海况和组装级别
    % 创建组合图例
    all_handles = [dummy_handles, vessel_type_handles, sea_condition_handles, assembly_level_handles];
    all_names = [process_names, vessel_type_names, sea_condition_names, assembly_level_names];

    % Create the legend with a smaller font size to fit all items
    legend(all_handles, all_names, 'Location', 'southoutside', 'Orientation', 'horizontal', 'FontSize', 6);    % 设置绘图属性
    grid on;
    title(title_text, 'FontSize', 14, 'FontWeight', 'bold');
    xlabel('时间 (小时)', 'FontSize', 12);

    % 计算工期并显示
    makespan = max(schedule.end_time);
    text(20, length(vessels) + berth_count + 2, ...
        sprintf('工期: %.2f 小时', makespan), ...
        'FontSize', 12, 'FontWeight', 'bold');

    % 如果船舶有类型信息，添加双船相关统计
    if isfield(vessels, 'type') && isfield(schedule, 'vessel_id')
        % 识别单体船和双体船索引
        single_hull_indices = find([vessels.type] == 1);
        dual_hull_indices = find([vessels.type] == 2);

        % 计算不同船舶类型的平均工序时间
        single_hull_times = [];
        dual_hull_times = [];

        for i = 1:length(schedule.process_id)
            if (schedule.process_id(i) > 0 && schedule.process_id(i) <= 4) || schedule.process_id(i) == 6
                vessel_idx = schedule.vessel_id(i);
                process_time = schedule.end_time(i) - schedule.start_time(i);

                % 按船舶类型收集
                if ismember(vessel_idx, single_hull_indices)
                    single_hull_times = [single_hull_times, process_time];
                elseif ismember(vessel_idx, dual_hull_indices)
                    dual_hull_times = [dual_hull_times, process_time];
                end
            end
        end

        % 计算平均时间（如果数组不为空）
        if ~isempty(single_hull_times)
            avg_single_hull = mean(single_hull_times);
        else
            avg_single_hull = NaN;
        end

        if ~isempty(dual_hull_times)
            avg_dual_hull = mean(dual_hull_times);
        else
            avg_dual_hull = NaN;
        end

        % 显示船舶类型统计信息
        y_offset = length(vessels) + berth_count + 3;

        % 显示船舶类型统计信息
        if ~isnan(avg_single_hull)
            text(20, y_offset, ...
                sprintf('单体船平均工序时间: %.2f 小时', avg_single_hull), ...
                'FontSize', 8);
        else
            text(20, y_offset, ...
                '单体船平均工序时间: 无数据', ...
                'FontSize', 8);
        end

        if ~isnan(avg_dual_hull)
            text(20, y_offset + 1, ...
                sprintf('双体船平均工序时间: %.2f 小时', avg_dual_hull), ...
                'FontSize', 8);
        else
            text(20, y_offset + 1, ...
                '双体船平均工序时间: 无数据', ...
                'FontSize', 8);
        end

        % 只有当两种船舶都有有效数据且单体船时间不为0时才计算效率提升
        if ~isnan(avg_single_hull) && ~isnan(avg_dual_hull) && avg_single_hull > 0
            text(20, y_offset + 2, ...
                sprintf('双体船完全组装效率提升: %.2f%%', (1 - avg_dual_hull/avg_single_hull) * 100), ...
                'FontSize', 8, 'FontWeight', 'bold');
        elseif ~isnan(avg_dual_hull) && isnan(avg_single_hull)
            text(20, y_offset + 2, ...
                '双体船完全组装效率提升: 无单体船数据用于比较', ...
                'FontSize', 8, 'FontWeight', 'bold');
        elseif isnan(avg_dual_hull) && ~isnan(avg_single_hull)
            text(20, y_offset + 2, ...
                '双体船完全组装效率提升: 无双体船数据用于比较', ...
                'FontSize', 8, 'FontWeight', 'bold');
        else
            text(20, y_offset + 2, ...
                '双体船完全组装效率提升: 无足够数据计算', ...
                'FontSize', 8, 'FontWeight', 'bold');
        end
    end    % 添加海况统计信息
    if isfield(schedule, 'sea_condition')
        % 计算不同海况下的平均工序时间
        good_sea_times = [];
        bad_sea_times = [];

        for i = 1:length(schedule.process_id)
            if (schedule.process_id(i) > 0 && schedule.process_id(i) <= 4) || schedule.process_id(i) == 6
                process_time = schedule.end_time(i) - schedule.start_time(i);
                sea_impact = schedule.sea_condition(i);

                if sea_impact < 0.33
                    good_sea_times = [good_sea_times, process_time];
                elseif sea_impact > 0.66
                    bad_sea_times = [bad_sea_times, process_time];
                end
            end
        end

        % 计算平均时间（如果数组不为空）
        if ~isempty(good_sea_times)
            avg_good_sea = mean(good_sea_times);
        else
            avg_good_sea = NaN;
        end

        if ~isempty(bad_sea_times)
            avg_bad_sea = mean(bad_sea_times);
        else
            avg_bad_sea = NaN;
        end

        % 显示海况统计信息
        y_offset = length(vessels) + berth_count + 3;

        if isfield(vessels, 'type')
            y_offset = y_offset + 3; % 如果已显示船舶类型统计，则向下移动
        end

        if ~isnan(avg_good_sea)
            text(800, y_offset, ...
                sprintf('良好海况平均工序时间: %.2f 小时', avg_good_sea), ...
                'FontSize', 8);
        elseif ~isempty(good_sea_times)
            text(800, y_offset, ...
                '良好海况平均工序时间: 无数据', ...
                'FontSize', 8);
        end

        if ~isnan(avg_bad_sea)
            text(800, y_offset + 1, ...
                sprintf('恶劣海况平均工序时间: %.2f 小时', avg_bad_sea), ...
                'FontSize', 8);
        elseif ~isempty(bad_sea_times)
            text(800, y_offset + 1, ...
                '恶劣海况平均工序时间: 无数据', ...
                'FontSize', 8);
        end

        % 显示海况影响
        if ~isnan(avg_good_sea) && ~isnan(avg_bad_sea) && avg_good_sea > 0
            text(800, y_offset + 2, ...
                sprintf('海况影响: %.2f%%', (avg_bad_sea/avg_good_sea - 1) * 100), ...
                'FontSize', 8, 'FontWeight', 'bold');
        elseif ~isnan(avg_bad_sea) && isnan(avg_good_sea)
            text(800, y_offset + 2, ...
                '海况影响: 无良好海况数据用于比较', ...
                'FontSize', 8, 'FontWeight', 'bold');
        elseif isnan(avg_bad_sea) && ~isnan(avg_good_sea)
            text(800, y_offset + 2, ...
                '海况影响: 无恶劣海况数据用于比较', ...
                'FontSize', 8, 'FontWeight', 'bold');
        elseif ~isempty(good_sea_times) || ~isempty(bad_sea_times)
            text(800, y_offset + 2, ...
                '海况影响: 无足够数据计算', ...
                'FontSize', 8, 'FontWeight', 'bold');
        end
    end

    % 设置轴限制 - 确保显示关键时间范围
    % 找到关键时间点
    if ~isempty(disruption) && isfield(disruption, 'time')
        % 显示故障前后的任务
        start_view = max(0, disruption.time - 100); % 显示故障前100小时
        end_view = makespan * 1.05;
        xlim([start_view, end_view]);
    else
        % 对于初始调度，保持原始视图
        xlim([0, makespan * 1.05]);
    end    % 确保合理的y轴范围，为统计信息留出额外空间
    if isfield(vessels, 'type') && isfield(schedule, 'assembly_level')
        ylim([0, length(vessels) + max(berth_count, 1) + 8]); % 为详细统计信息留出额外空间
    elseif isfield(vessels, 'type')
        ylim([0, length(vessels) + max(berth_count, 1) + 6]); % 为船舶类型统计信息留出额外空间
    else
        ylim([0, length(vessels) + max(berth_count, 1) + 3]);
    end

    % 添加更清晰的时间网格线
    set(gca, 'XGrid', 'on', 'XMinorGrid', 'on');
    if ~isempty(disruption) && isfield(disruption, 'time')
        % 在故障时间附近添加网格线
        set(gca, 'XTick', (floor(disruption.time/20)-5)*20:20:ceil(makespan/20)*20);
    else
        set(gca, 'XTick', 0:20:ceil(makespan/20)*20);  % 每20小时一个主网格线
    end

    set(gca, 'YTick', []);  % 移除y轴刻度标签，因为我们有自定义标签

    hold off;
end